const express = require("express");
const cors = require("cors");
const { fileParser } = require("express-multipart-file-parser");

// Controllers & Middlewares
const { silentLogin } = require("@/controllers/authController.js");
const inviteController = require("@/controllers/inviteController");
const { validateToken, validateTenant } = require("@/middlewares/authMiddleware.js");
const deleteController = require("@/controllers/deleteController");

const app = express();

app.use(
  cors({
    origin: true,
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Authorization", "Content-Type", "x-tenant-id"],
  })
);

app.use(fileParser({ rawBodyOptions: { limit: "10mb" } }));

// Auth
app.post("/silent-login", silentLogin);

// update user for digitorySooId from auth server
app.post("/invite/callback/:id", inviteController.inviteCallback);

// Tenant master (no :tenantId)
app.use("/tenants", require("./tenants.js"));

// Tenant scoped (with :tenantId)
app.use("/tenants/:tenantId", validateToken, validateTenant, require("./tenantsRouter.js"));

// delete by filter (only for dev testing)
app.post("/delete", deleteController.deleteByFilter);


module.exports = app;
