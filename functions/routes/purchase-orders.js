const express = require("express");
const router = express.Router({ mergeParams: true });

const { checkPrivilege } = require("@/middlewares/authMiddleware.js");
const { PRIV_CODES } = require("@/defs/privilegeDefs");
const purchaseOrderController = require("@/controllers/purchaseOrderController");

router.post(
  "/create",
  checkPrivilege(PRIV_CODES.PUR_PO),
  purchaseOrderController.createPurchaseOrder
);
router.post(
  "/:id/approve",
  checkPrivilege(PRIV_CODES.PUR_PO),
  purchaseOrderController.approvePurchaseOrder
);
router.post(
  "/:id/reject",
  checkPrivilege(PRIV_CODES.PUR_PO),
  purchaseOrderController.rejectPurchaseOrder
);

router.get(
  "/:id/pdf",
  checkPrivilege(PRIV_CODES.PUR_PO),
  purchaseOrderController.exportToPDF
);

router.get(
  "/:id/xlsx",
  checkPrivilege(PRIV_CODES.PUR_PO),
  purchaseOrderController.exportToXLSX
)

router.get(
  "/:id/email",
  checkPrivilege(PRIV_CODES.PUR_PO),
  purchaseOrderController.sendEmail
)

router.get(
  "/:id",
  checkPrivilege(PRIV_CODES.PUR_PO),
  purchaseOrderController.getPurchaseOrderById
);
router.post(
  "/",
  checkPrivilege(PRIV_CODES.PUR_PO),
  purchaseOrderController.getPurchaseOrders
);
router.put(
  "/:id",
  checkPrivilege(PRIV_CODES.PUR_PO),
  purchaseOrderController.updatePurchaseOrder
);

module.exports = router;
