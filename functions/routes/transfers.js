const express = require("express");
const router = express.Router({ mergeParams: true });

const transferController = require("@/controllers/transferController");

router.post("/", transferController.getTransfers);
router.post("/create", transferController.createTransfer);
router.get("/:id", transferController.getTransferById);
router.put("/:id/dispatch", transferController.dispatchTransfer);
router.put("/:id/receive", transferController.receiveTransfer);
router.put("/:id/close", transferController.closeTransfer);

router.get(
  "/:id/pdf",
//   checkPrivilege(PRIV_CODES.PUR_PO),
  transferController.exportToPDF
);

router.get(
  "/:id/xlsx",
//   checkPrivilege(PRIV_CODES.PUR_PO),
  transferController.exportToXLSX
)

module.exports = router;
