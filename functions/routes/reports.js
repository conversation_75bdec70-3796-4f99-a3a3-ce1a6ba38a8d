const express = require("express");
const router = express.Router({ mergeParams: true });

const reportController = require("@/controllers/reportController");
const {
  getStocks,
  getStockLedgers,
} = require("@/controllers/stockReportController");

router.get("/get-filter/:tenantId", reportController.filterFunction);
router.get("/get-base-filter/:reportTypeId", reportController.getReportConfig);

router.post("/stocks", getStocks);

router.post("/stock-ledgers", getStockLedgers);

//  procurement reports
router.use("/procurements", require("./procurement-reports"));

//  stock movement reports
router.use("/stockMovements", require("./stockMovement-reports"));

module.exports = router;
