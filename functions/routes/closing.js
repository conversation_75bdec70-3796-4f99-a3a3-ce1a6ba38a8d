const express = require("express");
const router = express.Router({ mergeParams: true });

const closingController = require("@/controllers/closingController");

router.post("/items", closingController.getClosingItems);
router.post("/", closingController.getClosingData);
router.post("/create", closingController.createClosing);
router.get("/:id", closingController.getClosingById);

module.exports = router;
