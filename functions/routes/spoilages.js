const express = require("express");
const router = express.Router({ mergeParams: true });

const spoilageController = require("@/controllers/spoilageController");

router.get("/:id", spoilageController.getSpoilageById);
router.post("/", spoilageController.getSpoilages);
router.post("/", spoilageController.insertSpoilage);
router.put("/:id", spoilageController.updateSpoilage);

module.exports = router;
