const express = require("express");
const router = express.Router({ mergeParams: true });

const adjustmentController = require("@/controllers/adjustmentController");

router.get("/:id", adjustmentController.getAdjustmentById);
router.post("/", adjustmentController.getAdjustments);
router.post("/", adjustmentController.insertAdjustment);
router.put("/:id", adjustmentController.updateAdjustment);

module.exports = router;
