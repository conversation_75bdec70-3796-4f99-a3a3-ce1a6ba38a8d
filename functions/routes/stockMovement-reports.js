/**
 * Stock Movement Reports Routes
 * --------------------------
 * Routes for tenant-level stock movement reports such as GRN, vendor, and purchase summaries.
 */

const express = require("express");
const router = express.Router({ mergeParams: true });

const { checkPrivilege } = require("@/middlewares/authMiddleware");
const { PRIV_CODES } = require("@/defs/privilegeDefs");

const stockMovementReportController = require("@/controllers/stockMovementReportsController")

// All routes assume :tenantId is provided in the parent route
// Example: /:tenantId/reports/stockMovements/...

// Transfer List Report
router.post(
    "/transfer-list-report",
    // checkPrivilege(PRIV_CODES.REP_GRN),
    stockMovementReportController.getTransferListReport
);

// Dispatch Transfer Report
router.post(
    "/dispatch-transfer-report",
    // checkPrivilege(PRIV_CODES.REP_GRN),
    stockMovementReportController.getDispatchTransferReport
);

// Detailed Transfer Report
router.post(
    "/detailed-transfer-report",
    // checkPrivilege(PRIV_CODES.REP_GRN),
    stockMovementReportController.getDetailedTransferReport
);

module.exports = router;
