// privileges.js
const { PRIV_CODES } = require("@/defs/privilegeDefs");

// Step 1: Grouped privileges by module
const groupedPrivileges = {
  "Product Configuration": [
    { code: PRIV_CODES.PC_VIEW, label: "View Product", description: "Allows viewing all product configurations." },
    { code: PRIV_CODES.PC_EDIT, label: "Edit Product", description: "Allows creating/editing product configurations." },
    { code: PRIV_CODES.PC_IMPORT, label: "Import Products", description: "Allows importing products." },
    { code: PRIV_CODES.PC_EXPORT, label: "Export Products", description: "Allows exporting products." },
  ],
  Setup: [
    { code: PRIV_CODES.SET_LOC, label: "Locations", description: "Manage inventory/operational locations." },
    { code: PRIV_CODES.SET_USER, label: "User Management", description: "Manage users, roles, and permissions." },
    { code: PRIV_CODES.CONTRACT, label: "Contracts", description: "Manage contracts." },
  ],
  Purchase: [
    { code: PRIV_CODES.PUR_PR, label: "Purchase Requests", description: "Create/edit/view purchase requests." },
    { code: PRIV_CODES.PUR_APPROVE_PR, label: "PR APPROVAL", description: "Approve purchase requests." },
    { code: PRIV_CODES.PUR_PO, label: "Purchase Orders", description: "Create/approve/manage purchase orders." },
    { code: PRIV_CODES.PUR_APPROVE_PO, label: "PO APPROVAL", description: "Approve purchase orders." },
    { code: PRIV_CODES.PUR_GRN, label: "GRN Management", description: "Manage GRNs and stock receipts." },
    { code: PRIV_CODES.PUR_INDENT, label: "INDENT Management", description: "Handle INDENT-related purchases." },
  ],
  Stock: [
    { code: PRIV_CODES.STK_VIEW, label: "Stock Overview", description: "View current stock levels and items." },
  ],
  Dashboard: [
    { code: PRIV_CODES.DASH_PUR, label: "Purchase Dashboard", description: "Overview of purchase activities." },
    { code: PRIV_CODES.DASH_COGS, label: "COGS Dashboard", description: "Overview of purchase activities." },
    { code: PRIV_CODES.DASH_INV, label: "Inventory Dashboard", description: "Overview of purchase activities." },
  ],
  Report: [
    { code: PRIV_CODES.REP_GRN, label: "GRN Reports", description: "Generate and view GRN reports." },
    { code: PRIV_CODES.REP_PO, label: "Purchase Order Reports", description: "Generate/view PO reports." },
    { code: PRIV_CODES.REP_STK, label: "Stock Reports", description: "Generate/view stock reports." },
  ],
};

function getAllPrivileges() {
  return Object.values(groupedPrivileges).flat();
}

function validateUserPrivilege(isAdmin, userPrivileges, requiredCodes) {
  if (isAdmin) return true;
  if (!Array.isArray(requiredCodes)) requiredCodes = [requiredCodes];
  return requiredCodes.every((code) => userPrivileges.includes(code));
}

function getPrivilegeDetails(codes) {
  const allPrivileges = getAllPrivileges();
  return codes.map((code) => allPrivileges.find((p) => p.code === code)).filter(Boolean);
}

module.exports = {
  groupedPrivileges,
  validateUserPrivilege,
  getPrivilegeDetails
};
