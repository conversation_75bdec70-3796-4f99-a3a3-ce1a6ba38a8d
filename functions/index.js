// Load environment variables from .env files using dotenv-flow
require("dotenv-flow").config();
require("module-alias/register");

const { onRequest } = require("firebase-functions/v2/https");
const { initializeApp, cert } = require("firebase-admin/app");
const { firebaseConfig, FIREBASE_STORAGE_BUCKET } = require("@/permission"); // Import service account or firebase config credentials

// Initialize Firebase Admin with service account credentials
// cert(firebaseConfig) expects the service account key object
initializeApp({
  credential: cert(firebaseConfig),
  storageBucket: FIREBASE_STORAGE_BUCKET,
});

// Import the main Express app (all routes are managed inside routes/index.js)
const app = require("@/routes/index.js");

// Export the Express app as a Firebase Cloud Function
// - Deployed to asia-south1 region
// - Exposed as an HTTPS endpoint
exports.app = onRequest(
  {
    region: "asia-south1",
    memory: "1GiB", // or "2GiB" if needed
    timeoutSeconds: 180, // maximum allowed in v2
  },
  app
);
