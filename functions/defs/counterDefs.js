/**
 * @typedef {Object} CounterType
 * @property {string} key - Unique key used in Firestore (e.g. "purchase")
 * @property {string} prefix - ID prefix used in formatted numbers (e.g. "P")
 */

/**
 * Supported counter types
 * @type {{
 *   PURCHASE_ORDER: CounterType,
 *   PURCHASE_REQUEST: CounterType,
 *   GRN: CounterType,
 *   VENDOR: CounterType,
 *   INVENTORY_ITEM: CounterType,
 *   RECIPE: CounterType,
 *   DISPATCH: CounterType,
 *   TRANSFER: CounterType,
 *   PACKAGE: CounterType,
 *   CLOSING: CounterType,
 *   CONTRACT: CounterType,
 *   SPOILAGE: CounterType,
 *   ADJUSTMENT: CounterType,
 * }}
 */
const COUNTER_TYPES = {
  PURCHASE_ORDER: { key: "po", prefix: "PO" },
  PURCHASE_REQUEST: { key: "pr", prefix: "PR" },
  GRN: { key: "grn", prefix: "GRN" },
  VENDOR: { key: "vendor", prefix: "V" },
  INVENTORY_ITEM: { key: "inventory_item", prefix: "I" },
  RECIPE: { key: "recipe", prefix: "R" },
  TRANSFER: { key: "transfer", prefix: "T" },
  DISPATCH: { key: "dispatch", prefix: "DIS" },
  PACKAGE: { key: "package", prefix: "PKG" },
  CLOSING: { key: "closing", prefix: "CLS" },
  CONTRACT: { key: "contract", prefix: "CT" },
  SPOILAGE: { key: "spoilage", prefix: "SPI" },
  ADJUSTMENT: { key: "adjustment", prefix: "ADJ" },
};

module.exports = {
  COUNTER_TYPES,
};
