/**
 * Procurement Report Definitions
 * -------------------------------
 * Centralized constants and configuration for all procurement reports.
 * Each report defines its columns, sort behavior, and optional aggregate keys.
 */

const REPORTS = Object.freeze({
  // Procurement reports
  GRN: "grn-report",
  DETAILED_GRN: "grn-detailed-report",
  LOCATION_WISE_GRN: "grn-location-wise-report",
  VENDOR_WISE_GRN: "grn-vendor-wise-report",
  CATEGORY_WISE_GRN: "grn-category-wise-report",
  SUB_CATEGORY_WISE_GRN: "grn-sub-category-wise-report",
  ITEM_WISE_GRN: "grn-item-wise-report",
  DAILY_GRN: "grn-daily-report",

  // Transfers reports
  TRANSFER_LIST: "transfer-list-report",
  DISPATCH_TRANSFER: "dispatch-transfer-report",
  DETAILED_TRANSFER: "detailed-transfer-report",
});

const AGGREGATE_TYPES = {
  "locationId": {
    id: "locationId",
    label: "locationName",
    columns: []
  },
  "inventoryLocationId": {
    id: "inventoryLocationId",
    label: "inventoryLocationName",
    columns: []
  },
  "vendorId": {
    id: "vendorName",
    label: "vendorName",
    columns: []
  },
  "date": {
    id: "date",
    label: "date",
    columns: ["grnDate"]
  },
  "grnDate": {
    id: "grnDate",
    label: "grnDate",
    columns: []
  },
  "categoryId": {
    id: "categoryId",
    label: "categoryName",
    columns: []
  },
  "subCategoryId": {
    id: "subCategoryId",
    label: "subCategoryName",
    columns: ["categoryId", "categoryName"]
  },
  "itemId": {
    id: ["itemId", "pkgId"],
    label: "itemName",
    columns: ["hsnCode", "itemCode", "pkg", "subCategoryId", "subCategoryName", "categoryId", "categoryName"]
  },
}

/**
 * @typedef {Object} ReportColumn
 * @property {string} key        - Key or path in data object
 * @property {string} header     - Column display name
 * @property {number} ordinal    - Display order (1..N)
 * @property {boolean} mandatory - Cannot be removed
 * @property {boolean} enable    - Enabled by default
 * @property {number} [width]    - Excel column width
 * @property {"left"|"center"|"end"} [align] - Optional alignment
 * @property {string} [destruct] - Optional field name to flatten nested arrays
 */

const AMOUNT_DETAIL_COLUMNS = [
  {
    header: "Gross Amount",
    key: "grossAmount",
    ordinal: 91,
    mandatory: false,
    enable: true,
    destruct: false,
    align: "end",
  },
  {
    header: "Discount",
    key: "totalDiscount",
    ordinal: 92,
    mandatory: false,
    enable: true,
    destruct: false,
    align: "end",
  },
  {
    header: "Cess",
    key: "totalCess",
    ordinal: 93,
    mandatory: false,
    enable: true,
    destruct: false,
    align: "end",
  },
  {
    header: "Net Amount",
    key: "netAmount",
    ordinal: 94,
    mandatory: false,
    enable: true,
    destruct: false,
    align: "end",
  },
  {
    header: "Foc",
    key: "totalFocAmount",
    ordinal: 95,
    mandatory: false,
    enable: true,
    destruct: false,
    align: "end",
  },
  // { header: "Charges", key: "charges", ordinal: 96, mandatory: false, enable: true, destruct: true, align: "end" },
  // { header: "Charge", key: "itemCharge", ordinal: 97, mandatory: false, enable: true, destruct: false, align: "end" },
  // { header: "Taxes", key: "taxes", ordinal: 98, mandatory: false, enable: true, destruct: true, align: "end" },
  {
    header: "Total Tax",
    key: "totalTaxAmount",
    ordinal: 99,
    mandatory: false,
    enable: true,
    destruct: false,
    align: "end",
  },
  {
    header: "Charges",
    key: "totalChargeAmount",
    ordinal: 100,
    mandatory: false,
    enable: true,
    destruct: false,
    align: "end",
  },
  {
    header: "Total",
    key: "totalAmount",
    ordinal: 101,
    mandatory: true,
    enable: true,
    destruct: false,
    align: "end",
  },
];

/**
 * Example Column Sets
 */
const REPORT_INFORMATION = Object.freeze({
  [REPORTS.GRN]: {
    id: REPORTS.GRN,
    name: "GRN REPORT",
    headers: [
      {
        header: "GRN No",
        key: "grnNumber",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      // { header: "PO No", key: "poNumber", ordinal: 2, mandatory: true, enable: true, destruct: false },
      {
        header: "GRN DATE",
        key: "grnDate",
        ordinal: 7,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Invoice No",
        key: "invoiceNumber",
        ordinal: 3,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Invoice Date",
        key: "invoiceDate",
        ordinal: 4,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Vendor",
        key: "vendorName",
        ordinal: 5,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      // { header: "Vendor Id", key: "vendorId", ordinal: 6, mandatory: false, enable: true, destruct: false },
      {
        header: "Location",
        key: "locationName",
        ordinal: 7,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Work/Storage Area",
        key: "inventoryLocationName",
        ordinal: 8,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Created At",
        key: "createdAt",
        ordinal: 9,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Created By",
        key: "createdByName",
        ordinal: 10,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      ...AMOUNT_DETAIL_COLUMNS,
      // { header: "PO Terms", key: "poTerms", ordinal: 18, mandatory: false, enable: true, destruct: false },
      // { header: "Payment Terms", key: "paymentTerms", ordinal: 19, mandatory: false, enable: true, destruct: false },
    ],
    aggregateId: null,
    options: {
      sortBy: "grnNumber",
      sortOrder: "desc",
    },
  },

  [REPORTS.DETAILED_GRN]: {
    id: REPORTS.DETAILED_GRN,
    name: "DETAILED GRN REPORT",
    headers: [
      {
        header: "GRN No",
        key: "grnNumber",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      // { header: "PO No", key: "poNumber", ordinal: 2, mandatory: true, enable: true, destruct: false },
      {
        header: "GRN DATE",
        key: "grnDate",
        ordinal: 7,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Invoice No",
        key: "invoiceNumber",
        ordinal: 3,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Invoice Date",
        key: "invoiceDate",
        ordinal: 4,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Vendor",
        key: "vendorName",
        ordinal: 5,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      // {
      //   header: "Vendor Id",
      //   key: "vendorId",
      //   ordinal: 6,
      //   mandatory: false,
      //   enable: true,
      //   destruct: false,
      // },
      {
        header: "Location",
        key: "locationName",
        ordinal: 7,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Work/Storage Area",
        key: "inventoryLocationName",
        ordinal: 8,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Created At",
        key: "createdAt",
        ordinal: 8,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Created By",
        key: "createdByName",
        ordinal: 9,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "ItemName",
        key: "itemName",
        ordinal: 10,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "ItemCode",
        key: "itemCode",
        ordinal: 11,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Category",
        key: "categoryName",
        ordinal: 12,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Sub Category",
        key: "subCategoryName",
        ordinal: 13,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      // { header: "Package", key: "pkg", ordinal: 14, mandatory: false, enable: true, destruct: false },
      // { header: "Order Quantity", key: "orderQuantity", ordinal: 15, mandatory: false, enable: true, destruct: false },
      {
        header: "Quantity",
        key: "qty",
        ordinal: 16,
        mandatory: false,
        enable: true,
        destruct: false,
        align: "end",
      },
      {
        header: "Package/UOM",
        key: "pkg",
        ordinal: 16,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Cost",
        key: "unitCost",
        ordinal: 17,
        mandatory: false,
        enable: true,
        destruct: false,
        align: "end",
      },
      ...AMOUNT_DETAIL_COLUMNS,
    ],
    aggregateId: null,
    options: {
      sortBy: "grnNumber",
      sortOrder: "desc",
    },
  },

  [REPORTS.VENDOR_WISE_GRN]: {
    id: REPORTS.VENDOR_WISE_GRN,
    name: "VENDOR-WISE GRN REPORT",
    headers: [
      {
        header: "Vendor",
        key: "vendorName",
        ordinal: 1,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      ...AMOUNT_DETAIL_COLUMNS,
    ],
    aggregateId: "vendorId",
    options: {
      sortBy: "totalAmount",
      sortOrder: "desc",
    },
  },

  [REPORTS.CATEGORY_WISE_GRN]: {
    id: REPORTS.CATEGORY_WISE_GRN,
    name: "CATEGORY-WISE GRN REPORT",
    headers: [
      {
        header: "Category",
        key: "categoryName",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      // { header: "Sub Category", key: "subCategory", ordinal: 2, mandatory: true, enable: true, destruct: false },
      ...AMOUNT_DETAIL_COLUMNS,
    ],
    aggregateId: "categoryId",
    options: {},
  },

  [REPORTS.SUB_CATEGORY_WISE_GRN]: {
    id: REPORTS.SUB_CATEGORY_WISE_GRN,
    name: "SUB-CATEGORY-WISE GRN REPORT",
    headers: [
      {
        header: "Sub Category",
        key: "subCategoryName",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Category",
        key: "categoryName",
        ordinal: 2,
        mandatory: true,
        enable: true,
        destruct: false,
      },

      // { header: "Sub Category", key: "subCategory", ordinal: 2, mandatory: true, enable: true, destruct: false },
      ...AMOUNT_DETAIL_COLUMNS,
    ],
    aggregateId: "subCategoryId",
    options: {},
  },

  [REPORTS.ITEM_WISE_GRN]: {
    id: REPORTS.ITEM_WISE_GRN,
    name: "ITEM-WISE GRN REPORT",
    headers: [
      {
        header: "Item Name",
        key: "itemName",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Item Code",
        key: "itemCode",
        ordinal: 2,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "HSN Code",
        key: "hsnCode",
        ordinal: 3,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Category",
        key: "categoryName",
        ordinal: 4,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Sub Category",
        key: "subCategoryName",
        ordinal: 5,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      // { header: "UOM", key: "uom", ordinal: 5, mandatory: true, enable: true, destruct: false },
      {
        header: "Quantity",
        key: "qty",
        ordinal: 7,
        mandatory: false,
        enable: true,
        destruct: false,
        align: "end",
      },
      {
        header: "Package/UOM",
        key: "pkg",
        ordinal: 8,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Unit Cost",
        key: "unitCost",
        ordinal: 9,
        mandatory: true,
        enable: true,
        destruct: false,
        align: "end",
      },
      ...AMOUNT_DETAIL_COLUMNS,
    ],
    aggregateId: "itemId",
    options: {},
  },

  [REPORTS.LOCATION_WISE_GRN]: {
    id: REPORTS.LOCATION_WISE_GRN,
    name: "LOCATION-WISE GRN REPORT",
    headers: [
      {
        header: "Location Name",
        key: "locationName",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      ...AMOUNT_DETAIL_COLUMNS,
    ],
    aggregateId: "locationId",
    options: {},
  },

  [REPORTS.DAILY_GRN]: {
    id: REPORTS.DAILY_GRN,
    name: "DAILY PURCHASE GRN REPORT",
    headers: [
      {
        header: "Date",
        key: "date",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      ...AMOUNT_DETAIL_COLUMNS,
    ],
    aggregateId: "date",
    options: {},
  },

  [REPORTS.TRANSFER_LIST]: {
    id: REPORTS.TRANSFER_LIST,
    name: "TRANSFER REPORT",
    headers: [
      {
        header: "Transfer No",
        key: "transferNo",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Workarea From",
        key: "from",
        ordinal: 2,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Workarea To",
        key: "to",
        ordinal: 3,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Created At",
        key: "createdAt",
        ordinal: 4,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Created By",
        key: "createdBy",
        ordinal: 5,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Dispatch Status",
        key: "dispatchStatus",
        ordinal: 6,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Receive Status",
        key: "receiveStatus",
        ordinal: 7,
        mandatory: false,
        enable: true,
        destruct: false,
      },
    ],
    aggregateKey: null,
    options: {
      sortBy: "transferNumber",
      sortOrder: "desc",
    },
  },

  [REPORTS.DISPATCH_TRANSFER]: {
    id: REPORTS.DISPATCH_TRANSFER,
    name: "DISPATCH TRANSFER REPORT",
    headers: [
      {
        header: "Transfer No",
        key: "transferNo",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Dispatch No",
        key: "dispatchNo",
        ordinal: 2,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Dispatched At",
        key: "dispatchedAt",
        ordinal: 3,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Dispatched By",
        key: "dispatchedBy",
        ordinal: 4,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Receive Status",
        key: "status",
        ordinal: 5,
        mandatory: false,
        enable: true,
        destruct: false,
      },
    ],
    aggregateKey: null,
    options: {
      sortBy: "transferNumber",
      sortOrder: "desc",
    },
  },

  [REPORTS.DETAILED_TRANSFER]: {
    id: REPORTS.DETAILED_TRANSFER,
    name: "DETAILED TRANSFER REPORT",
    headers: [
      {
        header: "Transfer No",
        key: "transferNo",
        ordinal: 1,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Workarea From",
        key: "from",
        ordinal: 2,
        mandatory: true,
        enable: true,
        destruct: false,
      },
      {
        header: "Workarea To",
        key: "to",
        ordinal: 3,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Created At",
        key: "createdAt",
        ordinal: 4,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Created By",
        key: "createdBy",
        ordinal: 5,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Item Name",
        key: "itemName",
        ordinal: 6,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Item Code",
        key: "itemCode",
        ordinal: 7,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Package",
        key: "pkg",
        ordinal: 8,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Unit Cost",
        key: "unitCost",
        ordinal: 9,
        mandatory: false,
        enable: true,
        destruct: false,
        align: "end",
      },
      {
        header: "Requested Quantity",
        key: "requestedQuantity",
        ordinal: 10,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Dispatched Quantity",
        key: "dispatchedQuantity",
        ordinal: 11,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Received Quantity",
        key: "receivedQuantity",
        ordinal: 12,
        mandatory: false,
        enable: true,
        destruct: false,
      },
      {
        header: "Shortage Quantity",
        key: "shortageQuantity",
        ordinal: 13,
        mandatory: false,
        enable: true,
        destruct: false,
      },
    ],
    aggregateKey: null,
    options: {
      sortBy: "transferNumber",
      sortOrder: "desc",
    },
  },
});

module.exports = {
  REPORTS,
  REPORT_INFORMATION,
  AGGREGATE_TYPES
};
