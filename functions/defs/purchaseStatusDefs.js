// purchaseStatus.js

/**
 * Enum of all possible purchase statuses
 */
const purchaseStatus = Object.freeze({
  DRAFT: "draft",
  SUBMITTED: "submitted",
  APPROVED: "approved",
  REJECTED: "rejected",
  PARTIAL: "partial",
  COMPLETED: "completed",
  DELETED: "deleted",
  RETURN_VENDOR: "returnVendor",
});

/**
 * Returns array of statuses considered "active"
 * Active = draft, submitted, approved (excluding completed, deleted, rejected)
 */
const activeStatuses = [
  purchaseStatus.DRAFT,
  purchaseStatus.SUBMITTED,
  purchaseStatus.APPROVED,
  purchaseStatus.PARTIAL,
];

module.exports = {
  purchaseStatus,
  activeStatuses,
};
