/**
 * @fileoverview
 * Centralized Firestore collection name definitions.
 * 
 * Use these constants instead of hardcoding collection names
 * throughout repositories and services.
 *
 * Example usage:
 *   const { COLLECTIONS } = require("@/defs/collectionDefs");
 *   const snapshot = await db.collection(COLLECTIONS.MENU_ITEMS).get();
 */

/**
 * @typedef {Object} CollectionDefs
 * @property {string} LOCATIONS           - Physical business locations (e.g., outlets or branches)
 * @property {string} WORK_AREAS          - Work areas within a location (e.g., kitchen, bar)
 * @property {string} CATEGORIES          - Menu categories (e.g., Beverages, Starters)
 * @property {string} SUB_CATEGORIES      - Subcategories under categories (e.g., Soft Drinks)
 * @property {string} MENU_ITEMS          - Menu items offered in the POS or app
 * @property {string} VENDORS             - Vendor or supplier master data
 * @property {string} TAGS                - tags master data
 */

/**
 * Firestore collection names (plural, snake_case)
 * @type {CollectionDefs}
 */
const COLLECTIONS = {
    GRN: "GRNs",
    ACCOUNT: "account",
    ADJUST_INVENTORY: "adjustInventory",
    CATEGORIES: "category",
    CLOSING: "closing",
    CONTRACTS: "contracts",
    COUNTERS: "counters",
    HOUSE_UNITS: "houseUnits",
    IMPORT_EXPORT_LOGS: "importExportLogs",
    INVENTORY_ITEMS: "inventoryItems",
    WORK_AREAS: "inventoryLocations",
    MENU_ITEMS: "menuItems",
    MODIFIERS: "modifiers",
    PURCHASE_ORDERS: "purchaseOrders",
    PURCHASE_REQUESTS: "purchaseRequests",
    RECEIPES: "receipes",
    ROLE: "role",
    SPOILAGES : "spoilages",
    LEDGERS: "stockLedgers",
    STOCKS: "stocks",
    LOCATIONS: "stores",
    TAGS: "tag",
    TAXES: "taxes",
    TENANT: "tenant",
    TRANSFERS: "transfers",
    USERS: "users",
    VENDORS: "vendors",
};

module.exports = { COLLECTIONS };
