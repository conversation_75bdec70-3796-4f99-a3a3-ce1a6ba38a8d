// defs/ledgerDefs.js

/**
 * @typedef {("GRN"|"PRD"|"TRANSFER-IN"|"TRANSFER-OUT"|"CONSUMPTION"|"ADJUSTMENT-CREDIT"|"ADJUSTMENT-DEBIT"|"CLOSING-CREDIT"|"CLOSING-DEBIT"|"SPOILAGE")} LedgerTypeKey
 * Represents the supported ledger/stock transaction types across the application.
 *
 * - "GRN"          : Goods Received Note - Stock received from a supplier
 * - "PRD"          : Production - Stock produced internally
 * - "TRANSFER_IN"  : Stock received from another outlet/location
 * - "TRANSFER_OUT" : Stock sent to another outlet/location
 * - "CONSUMPTION"  : Stock consumed in operations (e.g., recipes, production)
 * - "ADJUSTMENT_CREDIT"   : Manual stock adjustment due to loss, damage, or correction
 * - "ADJUSTMENT_DEBIT"   : Manual stock adjustment due to loss, damage, or correction
 * - "CLOSING_CREDIT"  : Manual stock-credit adjustment due to closing
 * - "CLOSING_DEBIT"   : Manual stock-debit adjustment due to closing
 * - "SPOILAGE"   : Manual stock-debit adjustment due to spoilage
 */

/**
 * Ledger types across the application.
 * Use this constant wherever ledger or stock transaction types are referenced.
 *
 * Example usage:
 * ```js
 * const { LedgerTypes } = require("@/defs/ledgerDefs");
 * const type = LedgerTypes.GRN;
 * ```
 */
const LedgerTypes = /** @type {Record<string, LedgerTypeKey>} */ (
  Object.freeze({
    /** Goods Received Note - Stock received from a supplier */
    GRN: "GRN",

    /** GRN deletion reversal */
    GRN_DELETE: "GRN-DELETE",

    /** Return to Vendor */
    RETURN_VENDOR: "RETURN-TO-VENDOR",

    /** Production - Stock produced internally */
    PRD: "PRODUCTION",

    /** Stock received from another outlet/location */
    TRANSFER_IN: "TRANSFER-IN",

    /** Stock sent to another outlet/location */
    TRANSFER_OUT: "TRANSFER-OUT",

    /** Stock consumed in operations */
    CONSUMPTION: "CONSUMPTION",

    /** Manual stock-credit adjustment */
    ADJUSTMENT_CREDIT: "ADJUSTMENT-CREDIT",

    /** Manual stock-debit adjustment */
    ADJUSTMENT_DEBIT: "ADJUSTMENT-DEBIT",

    /** Manual closing stock-credit adjustment */
    CLOSING_CREDIT: "CLOSING-CREDIT",

    /** Manual closing stock-debit adjustment */
    CLOSING_DEBIT: "CLOSING-DEBIT",

    /** Manual spoilage adjustment */
    SPOILAGE: "SPOILAGE",
  })
);

/**
 * @typedef {("IN"|"OUT")} StockTransactionTypeKey
 * Represents the direction of stock movement.
 *
 * - "IN"  : Stock coming into inventory (e.g., purchase, production, returns)
 * - "OUT" : Stock leaving inventory (e.g., sale, consumption, wastage)
 */

/**
 * Stock transaction types across the application.
 * Use this constant wherever a stock transaction type is referenced.
 *
 * Example usage:
 * ```js
 * const { StockTransactionType } = require("@/defs/stockDefs");
 * const movement = StockTransactionType.IN;
 * ```
 */
const StockTransactionType =
  /** @type {Record<string, StockTransactionTypeKey>} */ (
    Object.freeze({
      /** Stock added to inventory */
      IN: "IN",

      /** Stock removed from inventory */
      OUT: "OUT",
    })
  );

module.exports = {
  LedgerTypes,
  StockTransactionType,
};
