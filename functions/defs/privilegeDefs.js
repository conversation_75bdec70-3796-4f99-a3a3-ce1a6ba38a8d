// defs/privilegeDefs.js

/**
 * @typedef {(
 *   "PC_VIEW"   | "PC_EDIT"   | "PC_IMPORT"   | "PC_EXPORT" |
 *   "SET_LOC"   | "SET_USER"  |
 *   "PUR_PR"    | "PUR_APPROVE_PR" | "PUR_PO"  | "PUR_APPROVE_PO" | "PUR_GRN"     | "PUR_INDENT" | "PUR_DISPATCH" |
 *   "STK_VIEW"  |
 *   "DASH_PUR"  | "DASH_COGS" | "DASH_INV"    |
 *   "REP_GRN"   | "REP_PO"    | "REP_STK"
 *   "CONTRACT"  | "CLOSING"
 * )} PrivilegeCodeKey
 *
 * Represents all privilege codes used across the application.
 *
 * **Categories:**
 * - `PC_*`   : Product Config privileges  
 * - `SET_*`  : Setup privileges  
 * - `PUR_*`  : Purchase privileges  
 * - `STK_*`  : Stock privileges  
 * - `DASH_*` : Dashboard privileges  
 * - `REP_*`  : Report privileges  
 */

/**
 * Privilege codes across the application.
 * Use this constant wherever access control checks are performed.
 *
 * Example usage:
 * ```js
 * const { PRIV_CODES } = require("@/defs/privilegeDefs");
 * if (user.hasPrivilege(PRIV_CODES.PC_VIEW)) {
 *   // allow viewing
 * }
 * ```
 */
const PRIV_CODES = /** @type {Record<string, PrivilegeCodeKey>} */ (Object.freeze({
  // --- Product Config ---
  /** Permission to view product config */
  PC_VIEW: "PC_VIEW",

  /** Permission to edit product config */
  PC_EDIT: "PC_EDIT",

  /** Permission to import product config */
  PC_IMPORT: "PC_IMPORT",

  /** Permission to export product config */
  PC_EXPORT: "PC_EXPORT",

  // --- Setup ---
  /** Permission to manage locations */
  SET_LOC: "SET_LOC",

  /** Permission to manage users */
  SET_USER: "SET_USER",

  // --- Contracts ---
  CONTRACT: "SET_CT",

  // --- Closing ---
  CLOSING: "SET_CLS", // to do 

  // --- Purchase ---
  /** Purchase Requisition */
  PUR_PR: "PUR_PR",

  /** Purchase Order */
  PUR_PO: "PUR_PO",
  PUR_APPROVE_PR : "PUR_APPROVE_PR",

  /** Goods Receipt Note */
  PUR_GRN: "PUR_GRN",
  PUR_APPROVE_PO: "PUR_APPROVE_PO",

  /** Indent Management */
  PUR_INDENT: "PUR_INDENT",
  PUR_DISPATCH: "PUR_DISPATCH",

  // --- Stock ---
  /** View stock */
  STK_VIEW: "STK_VIEW",

  // --- Dashboard ---
  /** Purchase Dashboard */
  DASH_PUR: "DASH_PUR",
  /** COGS Dashboard */
  DASH_COGS: "DASH_COGS",
  /** Inventory Dashboard */
  DASH_INV: "DASH_INV",

  // --- Reports ---
  /** GRN Reports */
  REP_GRN: "REP_GRN",

  /** PO Reports */
  REP_PO: "REP_PO",

  /** Stock Reports */
  REP_STK: "REP_STK",

}));

module.exports = { 
  PRIV_CODES 
};
