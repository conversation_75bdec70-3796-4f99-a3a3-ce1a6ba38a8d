// repos/transferRepo.js
const admin = require("firebase-admin");
const db = admin.firestore();
const { COLLECTIONS } = require("@/defs/collectionDefs");

const TRANSFER_COLLECTION = COLLECTIONS.TRANSFERS;
const ADJUST_INVENTORY_COLLECTION = db.collection(COLLECTIONS.ADJUST_INVENTORY);

const {
  TIME_OPTION,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");

const { transferStatus } = require("@/defs/transferStatusDefs");
const { getInventoryItemStocks } = require("@/repositories/stockRepo");

/**
 * Get Firestore doc ref for a transfer
 */
function getTransferRef(id) {
  return db.collection(TRANSFER_COLLECTION).doc(id);
}

async function getTransferDataById(id) {
  const ref = getTransferRef(id);
  const doc = await ref.get();
  return doc.data();
}

async function getTransferByNumber(tenantId, number) {
  let query = db
    .collection(TRANSFER_COLLECTION)
    .where("tenantId", "==", tenantId)
    .where("transferNumber", "==", number)
    .limit(1);

  const snapshot = await query.get();
  if (snapshot.empty) return null;
  return snapshot.docs[0].data();
}

async function getDispatchByNumber(tenantId, number) {
  const snapshot = await db
    .collection(TRANSFER_COLLECTION)
    .where("tenantId", "==", tenantId)
    .get();

  for (const doc of snapshot.docs) {
    const data = doc.data();
    const dispatchData = data.timeLine?.some(t => t.dispatchNo === number);
    if (dispatchData) return data; 
  }
  return null;
}

/**
 * Create new transfer
 */
async function createTransfer(data, id = null) {
  const ref = id
    ? getTransferRef(id)
    : db.collection(TRANSFER_COLLECTION).doc();

  await ref.set({ ...data, id: ref.id });
  return { id: ref.id, ...data };
}

/**
 * Get transfer by ID
 */
async function getTransferById(id, type) {
  const ref = getTransferRef(id);
  const doc = await ref.get();
  // const doc = transaction ? await transaction.get(ref) : await ref.get();

  if (!doc.exists) {
    return null;
  }

  let data = { id: doc.id, ...doc.data() };

  if (data.requestedBy?.time) {
    data.requestedBy.time = FD.toFormattedDate(data.requestedBy.time);
  }

  if (type === "dispatch" && data.items?.length && data.issuer?.id) {
    try {
      const itemIdsWithPkg = data.items.map((item) => ({
        itemId: item.itemId,
        pkgId: item.pkg.id,
      }));

      // ✅ Step 3: Fetch stock for this work area
      const stockResult = await getInventoryItemStocks(
        data.issuer?.id,
        itemIdsWithPkg
      );

      data.items = data.items.map((item, ind) => {
        return {
          ...item,
          availableQuantity: stockResult[ind].inStock ?? 0,
        };
      });
    } catch (err) {
      console.error("Error fetching stock:", err);
    }
  }
  return data;
}

/**
 * Update transfer
 */
async function updateTransfer(id, data, transaction) {
  const ref = getTransferRef(id);
  if (transaction) {
    await transaction.update(ref, data);
  } else {
    await ref.update(data);
  }
  return { id, ...data };
}

/**
 * Get transfers by location (from/to)
 */
async function getTransfersByLocation(locationId) {
  const [fromSnapshot, toSnapshot] = await Promise.all([
    db
      .collection(TRANSFER_COLLECTION)
      .where("issuer.id", "==", locationId)
      .get(),
    db
      .collection(TRANSFER_COLLECTION)
      .where("requester.id", "==", locationId)
      .get(),
  ]);

  const uniqueDocs = new Map();
  [...fromSnapshot.docs, ...toSnapshot.docs].forEach((doc) => {
    uniqueDocs.set(doc.id, { id: doc.id, ...doc.data() });
  });

  return [...uniqueDocs.values()];
}

async function getTransfersRepo(tenantId, filters = {}) {
  if (!tenantId) throw new Error("tenantId is required");

  const statuses = filters.status || [];
  const hasPending = statuses.includes(transferStatus.PENDING);
  const hasCompleted = statuses.includes(transferStatus.COMPLETED);

  // -------------------------------
  // CASE 1: Both pending & completed
  // -------------------------------
  if (hasPending && hasCompleted) {
    const [pending, completed] = await Promise.all([
      fetchPendingTransfers(tenantId, filters),
      fetchCompletedTransfers(tenantId, filters),
    ]);

    // merge unique by id
    const map = new Map();
    [...pending, ...completed].forEach((t) => map.set(t.id, t));
    return Array.from(map.values());
  }

  if (hasPending) {
    return fetchPendingTransfers(tenantId, filters);
  }

  if (hasCompleted) {
    return fetchCompletedTransfers(tenantId, filters);
  }

  return [];
}

// 🔹 Helper for pending transfers
async function fetchPendingTransfers(tenantId, filters) {
  let query = db
    .collection(COLLECTIONS.TRANSFERS)
    .where("tenantId", "==", tenantId);

  // requesters filter
  if (filters.requester?.length) {
    query = query.where("requester.id", "in", filters.requester);
  }

  // issuers filter
  if (filters.issuer?.length) {
    query = query.where("issuer.id", "in", filters.issuer);
  }

  const snapshot = await query.get();
  let transfers = snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));

  // apply status filter: "pending" also means "partial"
  transfers = transfers.filter(
    (t) =>
      [transferStatus.PENDING, transferStatus.PARTIAL].includes(
        t.dispatchStatus
      ) ||
      [transferStatus.PENDING, transferStatus.PARTIAL].includes(t.receiveStatus)
  );

  return transfers;
}

// 🔹 Helper for completed transfers
async function fetchCompletedTransfers(tenantId, filters) {
  let query = db
    .collection(COLLECTIONS.TRANSFERS)
    .where("tenantId", "==", tenantId);

  // requesters filter
  if (filters.requester?.length) {
    query = query.where("requester.id", "in", filters.requester);
  }

  // issuers filter
  if (filters.issuer?.length) {
    query = query.where("issuer.id", "in", filters.issuer);
  }

  // date range filter
  if (filters.fromDate) {
    query = query.where(
      "requestedBy.time",
      ">=",
      FD.toFirestore(filters.fromDate, TIME_OPTION.START)
    );
  }
  if (filters.toDate) {
    query = query.where(
      "requestedBy.time",
      "<=",
      FD.toFirestore(filters.toDate, TIME_OPTION.END)
    );
  }

  const snapshot = await query.get();
  let transfers = snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));

  // keep only completed ones
  transfers = transfers.filter(
    (t) =>
      [transferStatus.COMPLETED].includes(t.dispatchStatus) ||
      [transferStatus.COMPLETED].includes(t.receiveStatus)
  );

  return transfers;
}

/**
 * Count of transfers pending receive actions
 */
async function getTransferReceiveCount(tenantId) {
  if (!tenantId) throw new Error("tenantId is required");
  const snapshot = await db
    .collection(COLLECTIONS.TRANSFERS)
    .where("tenantId", "==", tenantId)
    .where("receiveStatus", "!=", transferStatus.COMPLETED)
    .count()
    .get();

  return snapshot.data().count;
}

/**
 * Count of transfers pending dispatch actions
 */
async function getTransferDispatchCount(tenantId) {
  if (!tenantId) throw new Error("tenantId is required");
  const snapshot = await db
    .collection(COLLECTIONS.TRANSFERS)
    .where("tenantId", "==", tenantId)
    .where("dispatchStatus", "!=", transferStatus.COMPLETED)
    .count()
    .get();

  return snapshot.data().count;
}

/**
 * Insert shortage records into Firestore
 */
async function createShortageRecord(records) {
  for (const record of records) {
    const snap = await ADJUST_INVENTORY_COLLECTION.where(
      "dispatchNo",
      "==",
      record.dispatchNo
    )
      .where("tenantId", "==", record.tenantId)
      .get();

    if (!snap.empty) continue; // skip duplicates

    const ref = ADJUST_INVENTORY_COLLECTION.doc();
    await ref.set(record);
  }
}

module.exports = {
  createTransfer,
  getTransferById,
  updateTransfer,
  getTransfersByLocation,
  getTransferRef,
  getTransfersRepo,
  getTransferReceiveCount,
  getTransferDispatchCount,
  getTransferByNumber,
  createShortageRecord,
  getTransferDataById,
  getDispatchByNumber
};
