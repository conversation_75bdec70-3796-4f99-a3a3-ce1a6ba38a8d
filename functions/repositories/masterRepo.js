/**
 * @fileoverview Repository layer for fetching master data from Firestore.
 * Uses centralized collection definitions from CollectionDefs.js.
 */

const admin = require("firebase-admin");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const { DEFAULT_UNITS } = require("@/utils/defaultData");

const db = admin.firestore();

/**
 * Fetch all active locations for a tenant.
 * @param {string} tenantId - Tenant ID to filter locations
 * @returns {Promise<Array<{id: string, name: string}>>} Array of locations
 */
async function getLocations(tenantId) {
  const snapshot = await db
    .collection(COLLECTIONS.LOCATIONS)
    .where("tenantId", "==", tenantId)
    .where("activeStatus", "==", true)
    .orderBy("nameNormalized")
    .select("name", "inventoryLocationId")
    .get();

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
}

/**
 * Fetch all active work areas for a tenant.
 * @param {string} tenantId - Tenant ID to filter work areas
 * @returns {Promise<Array<{id: string, name: string, location_id: string, location_name: string, menu_tag: string, tag_id: string}>>} Array of work areas
 */
async function getInventoryLocations(tenantId) {
  const snapshot = await db
    .collection(COLLECTIONS.WORK_AREAS)
    .where("tenantId", "==", tenantId)
    .where("activeStatus", "==", true)
    .orderBy("nameNormalized")
    .select("name", "locationId", "locationName", "tagId", "isDefault")
    .get();

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
}

/**
 * Fetch all active categories and subcategories for a tenant.
 * Splits subcategories from the main category document.
 * @param {string} tenantId - Tenant ID to filter categories
 * @returns {Promise<{
 *   categories: Array<{id: string, name: string}>,
 *   subCategories: Array<{id: string, name: string, categoryId: string}>
 * }>} Object containing categories and subcategories arrays
 */
async function getCategoriesAndSubCategories(tenantId) {
  const snapshot = await db
    .collection(COLLECTIONS.CATEGORIES)
    .where("tenantId", "==", tenantId)
    .where("activeStatus", "==", true)
    .orderBy("nameNormalized")
    .select("name", "subCategories")
    .get();

  const categories = [];
  const subCategories = [];

  snapshot.docs.forEach((doc) => {
    const data = doc.data();
    categories.push({ id: doc.id, name: data.name });

    if (Array.isArray(data.subCategories)) {
      data.subCategories.forEach((sub) => {
        subCategories.push({
          id: sub.id,
          name: sub.name,
          categoryId: doc.id,
          categoryName: data.name,
        });
      });
    }
  });

  return { categories, subCategories };
}

/**
 * Fetch all active vendors for a tenant.
 * @param {string} tenantId - Tenant ID to filter vendors
 * @returns {Promise<Array<{id: string, name: string}>>} Array of vendors
 */
async function getVendors(tenantId) {
  const snapshot = await db
    .collection(COLLECTIONS.VENDORS)
    .where("tenantId", "==", tenantId)
    .where("activeStatus", "==", true)
    .orderBy("nameNormalized")
    .select("name")
    .get();

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
}

/**
 * Fetch all active menu items for a tenant.
 * Maps Firestore fields to consistent keys.
 * @param {string} tenantId - Tenant ID to filter menu items
 * @returns {Promise<Array<{
 *   id: string,
 *   name: string,
 *   categoryId: string,
 *   subCategoryId: string,
 *   menuTags: Array<string>,
 *   vendors: Array<string>
 * }>>} Array of menu items
 */
async function getInventoryItems(tenantId) {
  const snapshot = await db
    .collection(COLLECTIONS.INVENTORY_ITEMS)
    .where("tenantId", "==", tenantId)
    .where("activeStatus", "==", true)
    .orderBy("nameNormalized")
    .select(
      "itemName",
      "itemCode",
      "hsnCode",
      "category",
      "subCategory",
      "tags",
      "vendors",
      "packages",
      "purchaseUnit",
      "unitCost",
      "defaultPackage"
    )
    .get();

  return snapshot.docs.map((doc) => {
    const data = doc.data();
    let subtitle = data.itemCode;
    if (data.hsnCode) {
      subtitle += `, HSN Code: ${data.hsnCode}`;
    }
    return {
      id: doc.id,
      name: data.itemName,
      subtitle,
      code: data.itemCode,
      hsnCode: data.hsnCode,
      categoryId: data.category?.id,
      subCategoryId: data.subCategory?.id,
      menuTags: data.tags?.map((tag) => tag.id) || [],
      vendors: data.vendors?.map((vendor) => vendor.id) || [],
      purchaseUnit: data.purchaseUnit.symbol,
      defaultPackage: data.defaultPackage ?? true,
      unitCost: data.unitCost ?? 0,
      packages:
        data.packages?.map((pkg) => ({ id: pkg.id, name: pkg.name, unitCost: pkg.unitCost })) || [],
    };
  });
}

/**
 * Fetch all active tags for a tenant.
 * @param {string} tenantId - Tenant ID to filter tags
 * @returns {Promise<Array<{id: string, name: string}>>} Array of tags
 */
async function getTags(tenantId) {
  const snapshot = await db
    .collection(COLLECTIONS.TAGS)
    .where("tenantId", "==", tenantId)
    .where("activeStatus", "==", true)
    .orderBy("name")
    .select("name")
    .get();

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
}

/**
 * Fetch all active taxes for a tenant.
 * @param {string} tenantId - Tenant ID to filter taxes
 * @returns {Promise<Array<{id: string, name: string, components?: Array}>>} Array of taxes
 */
async function getTaxes(tenantId) {
  const snapshot = await db
    .collection(COLLECTIONS.TAXES)
    .where("tenantId", "==", tenantId)
    .where("activeStatus", "==", true)
    .orderBy("nameNormalized")
    .select("name", "components", "value")
    .get();

  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
}

/**
 * Fetch all active house units for a tenant.
 * @param {string} tenantId - Tenant ID to filter house units
 * @returns {Promise<Array<{id: string, name: string, symbol: string, toUnit: string, quantity: number}>>} Array of house units
 */
async function getHouseUnits(tenantId) {
  const snapshot = await db
    .collection(COLLECTIONS.HOUSE_UNITS)
    .where("tenantId", "==", tenantId)
    .where("activeStatus", "==", true)
    .orderBy("nameNormalized")
    .select("name", "symbol", "toUnit", "quantity", "default")
    .get();

  const houseUnits = snapshot.docs.map((doc) => ({
    id: doc.id,
    ...doc.data(),
  }));
  return [...DEFAULT_UNITS, ...houseUnits];
}

module.exports = {
  getLocations,
  getInventoryLocations,
  getCategoriesAndSubCategories,
  getVendors,
  getInventoryItems,
  getTags,
  getTaxes,
  getHouseUnits,
};
