const { COLLECTIONS} = require("@/defs/collectionDefs");
const QueryBuilder = require("@/helpers/queryBuilder");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");

/**
 * Fetches transfer report data with flexible filters.
 *
 * @param {string} tenantId - Tenant identifier (mandatory).
 * @param {object} filters - Filter options for date range, location, vendor, etc.
 * @returns {Promise<FirebaseFirestore.QuerySnapshot>} - Firestore query snapshot.
 */
const fetchTransferReportData = async (tenantId, filters = {}) => {
  const dateField = filters.reportDateType || "requestedBy.time";

  // Initialize QueryBuilder for transfers collection
  const q = new QueryBuilder(COLLECTIONS.TRANSFERS)
    .whereMust("tenantId", "==", tenantId)
    .whereIf(dateField, ">=", filters._fsFromDate)
    .whereIf(dateField, "<=", filters._fsToDate)
    // .whereIf("locationId", "in", filters.locations)
    // .whereIf("inventoryLocationId", "in", filters.inventoryLocations)
    // .whereIf("vendorId", "in", filters.vendors);

    //@todo: add select columns if needed

  // Execute query and return snapshot
  return await q.query.get();
};

module.exports = {
  fetchTransferReportData
};
