// repositories/tenantRepo.js
const { COLLECTIONS } = require("@/defs/collectionDefs");
const { getFirestore } = require("firebase-admin/firestore");
const db = getFirestore();

const TENANT_COLLECTION = db.collection(COLLECTIONS.TENANT);
const USER_COLLECTION = db.collection(COLLECTIONS.USERS);
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");

// ================== CREATE ==================
const saveTenant = async (tenantData) => {
  const docRef = TENANT_COLLECTION.doc();
  const data = { ...tenantData, id: docRef.id };
  await docRef.set(data);
  return { tenantId: docRef.id, emailId: data.emailId, name: data.name };
};

// ================== UPDATE ==================
const updateTenantById = async (id, data) => {
  const ref = TENANT_COLLECTION.doc(id);
  await ref.update(data);
  return { id, ...data };
};

const updateTenantSettingsById = async (id, settings) => {
  const docRef = TENANT_COLLECTION.doc(id);
  const snapshot = await docRef.get();
  if (!snapshot.exists) throw new Error("Tenant not found");

  const updates = Object.fromEntries(
    Object.entries(settings).map(([key, value]) => [`settings.${key}`, value])
  );

  await docRef.update(updates);
  return { id, updatedKeys: Object.keys(settings) };
};

const freezeMonthByTenantId = async (id) => {
  const ref = TENANT_COLLECTION.doc(id);
  const monthName = new Date().toLocaleString("default", { month: "long" });

  await ref.update({ "settings.currentMonth": monthName });
  return { id, currentMonth: monthName };
};

const updateTenantStatusById = async (id, isActive) => {
  const ref = TENANT_COLLECTION.doc(id);
  await ref.update({ activeStatus: isActive });
  return { id, activeStatus: isActive };
};

// ================== READ ==================
const getTenantById = async (id) => {
  const docRef = TENANT_COLLECTION.doc(id);
  const snapshot = await docRef.get();

  if (!snapshot.exists) return null;
  return snapshot.data();
};

const getAllTenants = async () => {
  const snapshot = await TENANT_COLLECTION.get();
  return snapshot.docs.map((doc) => {
    const data = doc.data();
    return {
      ...data,
      createdAt: FD.toFormattedDate(data.createdAt),
    };
  });
};

// ================== USER ==================
const checkDuplicateUserEmail = async (tenantId, emailId) => {
  const snapshot = await USER_COLLECTION.where("tenantId", "==", tenantId)
    .where("emailId", "==", emailId)
    .limit(1)
    .get();
  return !snapshot.empty;
};

const createUserForTenant = async (tenantId, tenantName, emailId) => {
  const userDocRef = USER_COLLECTION.doc();
  const userData = {
    id: userDocRef.id,
    tenantId,
    tenantName,
    emailId,
    activeStatus: true,
    digitorySsoId: "",
    isAdmin: true,
    roleName: "Admin",
  };
  await userDocRef.set(userData);
  return userDocRef.id;
};

// ================== INTERNAL UTILITY ==================
const getApprovalNeededStatus = async (tenantId) => {
  const doc = await TENANT_COLLECTION.doc(tenantId).get();
  if (!doc.exists) return false;
  const data = doc.data();
  return data.settings;
};

module.exports = {
  saveTenant,
  updateTenantById,
  updateTenantSettingsById,
  updateTenantStatusById,
  getTenantById,
  getAllTenants,
  createUserForTenant,
  checkDuplicateUserEmail,
  getApprovalNeededStatus,
  freezeMonthByTenantId,
  TENANT_COLLECTION,
};
