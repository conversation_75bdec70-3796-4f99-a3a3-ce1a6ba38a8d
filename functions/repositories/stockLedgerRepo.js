// repositories/stockLedgerRepo.js
const { getFirestore } = require("firebase-admin/firestore");
const db = getFirestore();
const {
  DATE_FORMAT,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");
const { paiseToRupee } = require("@/utils/money");

const LEDGER_COLLECTION = "stockLedgers";
const { getInventoryItem } = require("@/controllers/inventoryItemController");

/**
 * Adds a new entry to the StockLedger collection. Must be called with an active Firestore transaction.
 * @param {Object} entry - The entry to add. Must have the following properties:
 *   - referenceId: The ID of the item or order that this entry is for.
 *   - referenceType: "Item" or "Order".
 *   - quantity: The quantity of the item.
 *   - unitPrice: The unit price of the item.
 *   - totalPrice: The total price of the item (quantity * unitPrice).
 *   - type: "Credit" or "Debit".
 * @param {FirebaseFirestore.Transaction} t - The active transaction.
 * @returns {string} The ID of the newly added entry.
 * @throws {Error} If no transaction is provided.
 */
exports.addEntry = async function (entry, t) {
  if (!t) throw new Error("Transaction is required for addLedgerEntry");

  const ref = db.collection(LEDGER_COLLECTION).doc();
  t.set(ref, {
    ...entry,
    createdAt: FD.now(),
    updatedAt: FD.now(),
  });
  return ref.id;
};

/**
 * Updates the remaining quantity of a ledger entry.
 * @param {string} id - The Firestore document ID of the ledger entry to update.
 * @param {number} remainingQty - The new remaining quantity of the ledger entry.
 * @returns {Promise<void>} Resolves when the update is complete.
 */
exports.updateRemaining = async function (id, remainingQty) {
  const ref = db.collection(LEDGER_COLLECTION).doc(id);
  await ref.update({
    remainingQty,
    updatedAt: FD.now(),
  });
};

/**
 * Fetches available stock batches for a given item and inventory location.
 *
 * The batches are sorted using FEFO/FIFO logic:
 * - FEFO (First Expiry, First Out): batches with the earliest expiry date come first.
 * - FIFO fallback: within the same expiry date (or if expiry is not set), older batches (by createdAt) come first.
 *
 * @param {string} itemId - The unique identifier of the item.
 * @param {string} inventoryLocationId - The inventory location to fetch stock from.
 * @param {FirebaseFirestore.Transaction} [t] - Optional Firestore transaction object.
 * @returns {Promise<Array<Object>>} Array of batch objects, each containing the Firestore document ID as `id` and batch data.
 */

exports.getAvailableBatches = async function (itemId, inventoryLocationId, t) {
  console.log(
    `Fetching available batches for item ${itemId} in inventory location ${inventoryLocationId}`
  );
  const query = db
    .collection(LEDGER_COLLECTION)
    .where("itemId", "==", itemId)
    .where("inventoryLocationId", "==", inventoryLocationId)
    .where("remainingQty", ">", 0)
    .orderBy("expiryDate") // FEFO
    .orderBy("createdAt"); // FIFO fallback

  const snapshot = t ? await query.get({ transaction: t }) : await query.get();
  return snapshot.docs.map((d) => ({ id: d.id, ...d.data() }));
};

/**
 * Retrieves ledger entries linked to the given GRN ID and tenant ID.
 * - Queries ledger collection with the given tenant ID and GRN ID.
 * - Returns an array of ledger entries with their IDs and data.
 * @param {string} tenantId - Tenant ID to filter ledger entries by.
 * @param {string} grnId - Unique identifier of the GRN to retrieve ledger entries for.
 * @returns {Promise<Array<object>>} - Array of ledger entries with their IDs and data.
 */
exports.getLedgersByGRNId = async function (grnId, tenantId) {
  const snapshot = await db
    .collection(LEDGER_COLLECTION)
    .where("tenantId", "==", tenantId)
    .where("grnMeta.id", "==", grnId)
    .get();

  return snapshot.docs.map((d) => ({ id: d.id, ...d.data() }));
};

exports.getStockLedgers = async (tenantId, filters = {}) => {
  if (!tenantId) {
    throw new Error("tenantId is required");
  }

  let query = db
    .collection(LEDGER_COLLECTION)
    .where("tenantId", "==", tenantId);

  if (filters.locationId) {
    query = query.where("locationId", "==", filters.locationId);
  }
  if (filters.inventoryLocationId) {
    query = query.where(
      "inventoryLocationId",
      "==",
      filters.inventoryLocationId
    );
  }
  if (filters.categoryId) {
    query = query.where("categoryId", "==", filters.categoryId);
  }
  if (filters.subCategoryId) {
    query = query.where("subCategoryId", "==", filters.subCategoryId);
  }
  if (filters.itemId) {
    query = query.where("itemId", "==", filters.itemId);
  }

  const snapshot = await query.get();
  if (snapshot.empty) return [];

  return snapshot.docs.map((doc) => ({
    id: doc.id,
    ...doc.data(),
  }));
};

exports.findGrnItemPricesBetween = async ({
  tenantId,
  itemId,
  inventoryLocationId,
  pkgId,
}) => {
  let query = db
    .collection(LEDGER_COLLECTION)
    .where("tenantId", "==", tenantId)
    .where("ledgerType", "==", "GRN")
    .where("inventoryLocationId", "==", inventoryLocationId)
    .where("itemId", "==", itemId);

  if (pkgId) {
    query = query.where("pkg.id", "==", pkgId);
  }

  const snapshot = await query.orderBy("createdAt", "desc").limit(6).get();
  if (snapshot.empty) return [];

  return snapshot.docs.map((doc) => {
    const data = doc.data();
    return {
      grnId: data.grnMeta.id,
      grnNumber: data.grnMeta.grnNumber,
      date: FD.toFormattedDate(data.createdAt, DATE_FORMAT.DATE_ONLY),
      vendorId: data.grnMeta.vendorId,
      vendorName: data.grnMeta.vendorName,
      unitCost: paiseToRupee(data.unitCost),
      quantity: data.qty,
      pkg: data.pkg || "default",
      taxRate: data.taxRate || 0,
      UOM:
        pkgId && pkgId !== "default" ? data.pkg.packageCode : data.countingUOM,
      discount: data.discount ? paiseToRupee(data.discount) : 0,
    };
  });
};

exports.findLastGrnItemPrice = async ({
  tenantId,
  itemId,
  inventoryLocationId,
  pkgId,
}) => {
  let query = db
    .collection(LEDGER_COLLECTION)
    .where("tenantId", "==", tenantId)
    .where("ledgerType", "==", "GRN")
    .where("inventoryLocationId", "==", inventoryLocationId)
    .where("itemId", "==", itemId);

  if (pkgId) {
    query = query.where("pkg.id", "==", pkgId);
  }

  const snapshot = await query.orderBy("createdAt", "desc").limit(1).get();

  // If no GRN found → fetch master item and fallback
  if (snapshot.empty) {
    const item = await getInventoryItem(itemId);

    let pkgInfo = null;
    if (pkgId && pkgId !== "default") {
      pkgInfo = item.packages?.find((p) => p.id === pkgId) || null;
    }
    return {
      grnId: null,
      grnNumber: null,
      date: null,
      unitCost: pkgInfo ? pkgInfo.unitCost : item.unitCost,
      quantity: 0,
      pkg: "default",
      UOM: item.countingUOM,
    };
  }

  // GRN exists → return ledger data
  const data = snapshot.docs[0].data();
  return {
    grnId: data.grnMeta.id,
    grnNumber: data.grnMeta.grnNumber,
    date: FD.toFormattedDate(data.createdAt, DATE_FORMAT.DATE_ONLY),
    unitCost: paiseToRupee(data.unitCost),
    quantity: data.qty,
    pkg: data.pkg.name,
    UOM: pkgId && pkgId !== "default" ? data.pkg.packageCode : data.countingUOM,
  };
};
