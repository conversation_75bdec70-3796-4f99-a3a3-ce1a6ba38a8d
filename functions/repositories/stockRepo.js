// repositories/stockRepo.js
const admin = require("firebase-admin");
const db = admin.firestore();

const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");

const STOCK_COLLECTION = "stocks";

const getDocId = (inventoryLocationId, itemId, pkgId) =>
  `${inventoryLocationId}_${itemId}_${pkgId}`;

/**
 * Increases the stock of the given item in the given inventory location.
 *
 * If the item does not exist in the inventory location, it creates a new stock entry.
 * If the item already exists, it updates the existing stock entry.
 *
 * @param {Object} payload - { itemCode, inventoryLocationId, qty, cost, uom }
 * @param {firebase.firestore.Transaction} t - Firestore transaction object
 */
exports.increaseStock = async function (payload, t) {
  if (!t) throw new Error("Transaction is required for increaseStock");

  const { itemId, inventoryLocationId, qty, totalCost, pkg: package } = payload;
  const docId = getDocId(inventoryLocationId, itemId, package.id);
  const ref = db.collection(STOCK_COLLECTION).doc(docId);

  // @todo correct this
  const doc = await ref.get();

  if (!doc.exists) {
    // First stock entry
    t.set(ref, {
      tenantId: payload.tenantId,
      groupKey: `${inventoryLocationId}_${itemId}`,
      locationId: payload.locationId,
      locationName: payload.locationName,
      inventoryLocationId: payload.inventoryLocationId,
      inventoryLocationName: payload.inventoryLocationName,
      categoryId: payload.categoryId,
      subcategoryId: payload.subcategoryId,
      categoryName: payload.categoryName,
      subcategoryName: payload.subcategoryName,
      itemId: payload.itemId,
      itemCode: payload.itemCode,
      itemName: payload.itemName,
      countingUOM: payload.countingUOM,
      recipeUOM: payload.recipeUOM || "", // @todo validate
      conversionFactor: payload.conversionFactor || 1, // @todo validate
      qty,
      avgCost: totalCost,
      totalValue: totalCost,
      parLevel: 0,
      createdAt: FD.now(),
      updatedAt: FD.now(),
      pkg: payload.pkg,
      remarks: payload.remarks,
    });
    return;
  }

  const data = doc.data();
  const newQty = data.qty + qty;
  const newTotal = data.totalValue + totalCost;

  t.update(ref, {
    qty: newQty,
    totalValue: newTotal,
    avgCost: newTotal / newQty,
    updatedAt: FD.now(),
    pkg: payload.pkg,
  });
};

/**
 * Decreases the stock of the given item in the given inventory location.
 * If the item does not exist in the inventory location, it throws an error.
 * If the item already exists, it updates the existing stock entry.
 * Throws an error if the item does not have sufficient stock.
 *
 * @param {Object} payload - { itemCode, inventoryLocationId, qty }
 * @param {firebase.firestore.Transaction} t - Firestore transaction object
 */
exports.decreaseStock = async function (
  { itemId, inventoryLocationId, packageId, qty },
  t
) {
  if (!t) throw new Error("Transaction is required for decreaseStock");

  const docId = getDocId(inventoryLocationId, itemId, packageId);
  const ref = db.collection(STOCK_COLLECTION).doc(docId);

  // @todo correct this
  const doc = await ref.get();
  if (!doc.exists) throw new Error("Stock not found");

  const data = doc.data();
  if (data.qty < qty) throw new Error("Insufficient stock");

  const newQty = data.qty - qty;
  const newTotal = data.avgCost * newQty;
  t.update(ref, {
    qty: newQty,
    totalValue: newTotal,
    updatedAt: FD.now(),
  });
};

/**
 * Fetch stocks with tenant & dynamic filters
 * @param {string} tenantId - Mandatory tenant identifier
 * @param {Object} filters { locationId, inventoryLocationId, categoryId, subCategoryId, itemId }
 * @returns {Promise<Array>}
 */
exports.getStocks = async (tenantId, filters = {}) => {
  if (!tenantId) {
    throw new Error("tenantId is required");
  }

  let query = db.collection(STOCK_COLLECTION).where("tenantId", "==", tenantId);

  if (filters.locations?.length) {
    query = query.where("locationId", "in", filters.locations);
  }
  if (filters.inventoryLocations?.length) {
    query = query.where(
      "inventoryLocationId",
      "in",
      filters.inventoryLocations
    );
  }
  if (filters.categories?.length) {
    query = query.where("categoryId", "in", filters.categories);
  }
  if (filters.subCategories?.length) {
    query = query.where("subcategoryId", "in", filters.subCategories);
  }
  if (filters.inventoryItems?.length) {
    query = query.where("itemId", "in", filters.inventoryItems);
  }

  const snapshot = await query.get();
  if (snapshot.empty) return [];

  return snapshot.docs.map((doc) => ({
    id: doc.id,
    ...doc.data(),
  }));
};

/**
 * Fetch stocks with tenant & dynamic filters
 * @param {string} inventoryLocationId - Mandatory inventory location identifier
 * @param {Array} items - Array of item objects with at least `itemId` property
 * @returns {Promise<Array>}
 */

exports.getInventoryItemStocks = async (inventoryLocationId, items = []) => {
  if (!inventoryLocationId || !items.length) {
    throw new Error("inventoryLocationId, and items are required");
  }

  const requiredStockIds = [];
  for (const item of items) {
    if (!item.itemId) {
      throw new Error("itemId is required for all items");
    }
    const stockId = getDocId(inventoryLocationId, item.itemId, item.pkgId);
    item.stockId = stockId;
    requiredStockIds.push(stockId);
  }

  // Fetch documents by their document IDs
  const stockPromises = requiredStockIds.map((stockId) =>
    db.collection(STOCK_COLLECTION).doc(stockId).get()
  );

  const stockDocs = await Promise.all(stockPromises);

  // Build map: itemId -> qty
  const stockMap = new Map();
  for (const doc of stockDocs) {
    if (doc.exists) {
      const data = doc.data();
      stockMap.set(`${data.itemId}_${data.pkg.id}`, data.qty);
    }
  }

  // Add inStock to items
  for (const item of items) {
    item.inStock = stockMap.get(`${item.itemId}_${item.pkgId}`) ?? 0;
  }

  return items;
};
