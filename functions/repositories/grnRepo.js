// repos/grnRepo.js

const { getFirestore } = require("firebase-admin/firestore");
const db = getFirestore();
const { COLLECTIONS } = require("@/defs/collectionDefs");

// Constant reference to GRNs collection
const GRNS_COLLECTION = db.collection(COLLECTIONS.GRN);
const {
  DATE_FORMAT,
  TIME_OPTION,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");
const { paiseToRupee } = require("@/utils/money");

/**
 * Retrieves a single GRN document by its ID and tenant ID.
 * @param {string} grnId - Unique identifier of the GRN document to retrieve.
 * @param {string} tenantId - Tenant ID to filter GRNs by.
 * @param {firebase.firestore.Transaction} transaction - Optional transaction to use.
 * @returns {Promise<object>} - Retrieved GRN document or null if not found.
 */
async function getGRNById(grnId, tenantId, transaction) {
  const ref = GRNS_COLLECTION.doc(grnId);
  const doc = transaction ? await transaction.get(ref) : await ref.get();
  if (!doc.exists) return null;
  const data = doc.data();

  if (data.tenantId !== tenantId) return null;

  return {
    ...data,
    grnId: doc.id,
    grnDate: FD.toFormattedDate(data.grnDate, DATE_FORMAT.DATE_ONLY),
    invoiceDate: FD.toFormattedDate(data.invoiceDate, DATE_FORMAT.DATE_ONLY),
    createdAt: FD.toFormattedDate(data.createdAt),
    removedAt: data.removedAt ? FD.toFormattedDate(data.removedAt) : null,
    updatedAt: FD.toFormattedDate(data.updatedAt),
    totalAmount: paiseToRupee(data.totalAmount),
    totalDiscount: paiseToRupee(data.totalDiscount),
    totalCess: paiseToRupee(data.totalCess),
    totalFocAmount: paiseToRupee(data.totalFocAmount),
    totalTaxAmount: paiseToRupee(data.totalTaxAmount),
    totalChargeAmount: paiseToRupee(data.totalChargeAmount),
    grossAmount: paiseToRupee(data.grossAmount),
    netAmount: paiseToRupee(data.netAmount),
    taxes: data.taxes || [],
    charges: data.charges || [],
    items: data.items.map((item) => ({
      ...item,
      orderedQty: item.qtyOrdered,
      receivedQty: item.qtyReceived,
      pkg: item.pkg,
      uom: item.pkg?.id !== "default" ? item.pkg.name : item.purchaseUOM,
      unitCost: paiseToRupee(item.unitCost),
      totalAmount: paiseToRupee(item.totalAmount),
      totalTaxAmount: paiseToRupee(item.totalTaxAmount),
      totalDiscount: paiseToRupee(item.totalDiscount),
      totalCess: paiseToRupee(item.totalCess),
      grossAmount: paiseToRupee(item.grossAmount),
      totalChargeAmount: paiseToRupee(item.totalChargeAmount),
      totalFocAmount: paiseToRupee(item.totalFocAmount),
      netAmount:
        paiseToRupee(item.unitCost) * item.qtyReceived -
        paiseToRupee(item.totalDiscount),
    })),
  };
}

async function getGRNByNumber(tenantId, grnNumber) {
  let query = GRNS_COLLECTION.where("tenantId", "==", tenantId)
    .where("grnNumber", "==", grnNumber)
    .limit(1);

  const snapshot = await query.get();
  if (snapshot.empty) return null;
  return snapshot.docs[0].data();
}

/**
 * Lists GRN documents based on the given filters.
 * - Filters GRNs by tenantId.
 * - Optionally filters GRNs by locationId, startDate, and endDate.
 * - The startDate and endDate filters are inclusive.
 * @param {object} filters - Object containing filters to apply.
 * @param {string} filters.tenantId - Required. Tenant ID to filter GRNs by.
 * @param {string} [filters.locations] - Optional. Location ID to filter GRNs by.
 * @param {string} [filters.inventoryLocations] - Optional. Inventory Location ID to filter GRNs by.
 * @param {string} [filters.fromDate] - Optional. Start date to filter GRNs by.
 * @param {string} [filters.toDate] - Optional. End date to filter GRNs by.
 * @returns {Promise<Array<object>>} - List of GRN documents or empty array if no results are found.
 */
async function listGRNs({
  tenantId,
  locations,
  inventoryLocations,
  fromDate,
  toDate,
  attachments,
}) {
  let query = GRNS_COLLECTION.where("tenantId", "==", tenantId);

  if (locations?.length) query = query.where("location.id", "in", locations);
  if (inventoryLocations?.length)
    query = query.where("inventoryLocation.id", "in", inventoryLocations);

  if (fromDate) {
    query = query.where(
      "createdAt",
      ">=",
      FD.toFirestore(fromDate, TIME_OPTION.START)
    );
  }
  if (toDate) {
    query = query.where(
      "createdAt",
      "<=",
      FD.toFirestore(toDate, TIME_OPTION.END)
    );
  }

  if (attachments === "noAttachments") {
    // Only documents with NO attachments
    query = query.where("attachments", "==", null);
  } else if (attachments === "attachments") {
    // Only documents WITH attachments
    query = query.where("attachments", "!=", null);
  }

  const snapshot = await query.get();
  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
}

/**
 * Creates a new GRN document with the given data
 * @param {string} grnId - ID of the GRN document to create
 * @param {object} grnData - GRN data to be created
 * @param {firebase.firestore.Transaction} transaction - Optional transaction to use
 * @returns {Promise<object>} - Newly created GRN document
 */
async function createGRN(grnId, grnData, transaction) {
  const ref = GRNS_COLLECTION.doc(grnId);
  const payload = {
    ...grnData,
    tenantId: grnData.tenantId,
    createdAt: FD.now(),
    updatedAt: FD.now(),
  };

  if (transaction) {
    await transaction.set(ref, payload);
  } else {
    await ref.set(payload);
  }

  return { grnId, ...payload };
}

const updateAttachments = async (id, attachments) => {
  const ref = GRNS_COLLECTION.doc(id);
  const doc = await ref.get();

  if (!doc.exists) {
    throw new Error("Grn not found");
  }

  const existingAttachments = doc.data().attachments || [];

  const updatedAttachments = [
    ...existingAttachments.filter(
      (f) =>
        !attachments.some(
          (s) => s.filePath === f.filePath || s.fileName === f.fileName
        )
    ),
    ...attachments,
  ];

  await ref.update({ attachments: updatedAttachments });

  return updatedAttachments;
};

const deleteAttachment = async (id, filePath) => {
  const ref = GRNS_COLLECTION.doc(id);
  const doc = await ref.get();

  if (!doc.exists) throw new Error("Grn not found");

  const attachments = doc.data().attachments || [];
  const updatedAttachments = attachments.filter(
    (f) => f.filePath !== filePath
  );

  await ref.update({
    attachments: updatedAttachments.length > 0 ? updatedAttachments : null
  });

  return updatedAttachments;
};


module.exports = {
  getGRNById,
  listGRNs,
  createGRN,
  getGRNByNumber,
  updateAttachments,
  deleteAttachment,
};
