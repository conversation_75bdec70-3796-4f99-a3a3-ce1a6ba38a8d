// repos/transferRepo.js
const admin = require("firebase-admin");
const db = admin.firestore();
const { LedgerTypes } = require("@/defs/ledgerDefs");
const { creditStock } = require("@/services/stockService");
const { getInventoryItemStocks } = require("@/repositories/stockRepo");
const CLOSING_COLLECTION = "closing";
const INVENTORY_COLLECTION = "inventoryItems";
const INVENTORY_LOCATIONS_COLLECTION = "inventoryLocations";
const {
  TIME_OPTION,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");

async function fetchClosingData({
  tenantId,
  locations,
  inventoryLocations,
  fromDate,
  toDate,
  stockCorrection,
}) {
  let query = db.collection(CLOSING_COLLECTION).where("tenantId", "==", tenantId);

  if (locations?.length) query = query.where("locationId", "in", locations);
  if (inventoryLocations?.length)
    query = query.where("workAreaId", "in", inventoryLocations);

  if (fromDate) {
    query = query.where(
      "closedBy.time",
      ">=",
      FD.toFirestore(fromDate, TIME_OPTION.START)
    );
  }
  if (toDate) {
    query = query.where(
      "closedBy.time",
      "<=",
      FD.toFirestore(toDate, TIME_OPTION.END)
    );
  }
  if (stockCorrection === "yes") {
    query = query.where("stockCorrection", "==", true);
  } else if (stockCorrection === "no") {
    query = query.where("stockCorrection", "==", false);
  }

  const snapshot = await query.get();
  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
}

async function getById(id) {
  try {
    const docRef = db.collection(CLOSING_COLLECTION).doc(id);
    const snapshot = await docRef.get();

    if (!snapshot.exists) return null;
    return snapshot.data();
  } catch (err) {
    throw Error(err.message);
  }
}

async function fetchClosingItems(tenantId, locationId) {
  if (!tenantId) throw new Error("tenantId is required");
  if (!locationId) throw new Error("locationId is required");
  // Get location data
  const workArea = await db
    .collection(INVENTORY_LOCATIONS_COLLECTION)
    .where("id", "==", locationId)
    .get();
  if (workArea.empty) {
    console.log("No matching locations found", locationId);
    return [];
  }
  const locationData = workArea.docs[0].data();
  let query = db
    .collection(INVENTORY_COLLECTION)
    .where("tenantId", "==", tenantId);

  // Add tag filter if tagId exists
  if (locationData.tagId) {
    query = query.where("tags", "array-contains", locationData.tagId);
  }
  const result = await query.get();
  return result.docs.map((doc) => ({
    id: doc.id,
    ...doc.data(),
  }));
}

async function createClosing(data) {
  const ref = db.collection(CLOSING_COLLECTION).doc();
  await ref.set({ ...data, id: ref.id });
  return { id: ref.id, ...data };
}

async function getClosingByNumber(tenantId, closingNumber) {
  let query = db
    .collection(CLOSING_COLLECTION)
    .where("tenantId", "==", tenantId)
    .where("closingNumber", "==", closingNumber)
    .limit(1);

  const snapshot = await query.get();
  if (snapshot.empty) return null;
  return snapshot.docs[0].data();
}

module.exports = {
  fetchClosingItems,
  createClosing,
  fetchClosingData,
  getById,
  getClosingByNumber,
};
