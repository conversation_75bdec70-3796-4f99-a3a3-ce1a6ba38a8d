const admin = require("firebase-admin");
const db = admin.firestore();
const {
  TIME_OPTION,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");

const SPOILAGE_COLLECTION = "spoilages";

const saveSpoilage = async (data) => {
  const ref = db.collection(SPOILAGE_COLLECTION).doc();
  const result = await ref.set({ ...data, id: ref.id });
  return result;
};

async function getAllSpoilages({
  tenantId,
  locations,
  inventoryLocations,
  fromDate,
  toDate
}) {
  let query = db.collection(SPOILAGE_COLLECTION).where("tenantId", "==", tenantId);

  if (locations?.length) query = query.where("locationId", "in", locations);
  if (inventoryLocations?.length)
    query = query.where("workAreaId", "in", inventoryLocations);

  if (fromDate) {
    query = query.where(
      "requestedBy.time",
      ">=",
      FD.toFirestore(fromDate, TIME_OPTION.START)
    );
  }
  if (toDate) {
    query = query.where(
      "requestedBy.time",
      "<=",
      FD.toFirestore(toDate, TIME_OPTION.END)
    );
  }

  const snapshot = await query.get();
  return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
}

const getById = async (id) => {
  const ref = db.collection(SPOILAGE_COLLECTION).doc(id);
  const doc = await ref.get();
  return doc.exists ? { ...doc.data() } : null;
};

const updateById = async (id, data) => {
  const ref = db.collection(SPOILAGE_COLLECTION).doc(id);
  const result = await ref.update(data);
  return result;
};

module.exports = { saveSpoilage, getAllSpoilages, getById, updateById };
