const PdfPrinter = require("pdfmake");
const {truncateNumber } = require("@/utils/money");


function generatePrint(data) {
  const fonts = getFonts();
  const printer = new PdfPrinter(fonts);

  const docDefinition = {
    pageSize: "A4",
    pageOrientation: "portrait",
    pageMargins: [10.5, 10, 10.5, 15],
    background: getBackground(),
    content: data,
    styles: getStyles(),
    defaultStyle: { font: "Helvetica", fontSize: 9 },
  };

  return new Promise((resolve, reject) => {
    const pdfDoc = printer.createPdfKitDocument(docDefinition);
    const chunks = [];

    pdfDoc.on("data", (chunk) => chunks.push(chunk));
    pdfDoc.on("end", () => {
      resolve(Buffer.concat(chunks));
    });
    pdfDoc.on("error", reject);

    pdfDoc.end();
  });
}

// ---------- Background ----------
const getBackground = () => function (currentPage, pageSize) {
  return {
    canvas: [
      {
        type: "rect",
        x: 10,
        y: 10,
        w: pageSize.width - 20,
        h: pageSize.height - 20,
        lineWidth: 1.2,
        lineColor: "#000",
      },
    ],
  };
};

const getFonts = () => ({
  Helvetica: {
    normal: "Helvetica",
    bold: "Helvetica-Bold",
    italics: "Helvetica-Oblique",
    bolditalics: "Helvetica-BoldOblique",
  },
});

const getBorderLayout = () => ({
  hLineWidth: () => 0.5,  
  vLineWidth: (i, node) =>
    i === 0 || i === node.table.widths.length ? 0 : 0.5,  
  hLineColor: () => "#000",
  vLineColor: () => "#000",
});

const getStyles = () => ({
  header: { fontSize: 16, bold: true, color: "#000" },
  rightHeader: { fontSize: 14, bold: true, color: "#000" },
  tableHeader: {
    fontSize: 10,
    bold: true,
    alignment: "center",
    margin: [3, 3, 3, 3],
  },
  labelStyle: { fontSize: 9, bold: false, color: "#333" },
  cellStyle: { fontSize: 9, margin: [3, 3, 3, 3] },
  grandTotalStyle: { fontSize: 9, bold: true, margin: [3, 3, 3, 3] },
});

const generateHeaderName = (name) => ({
  text: `${name}`,
  style: "tableHeader",
  fillColor: "#5c8373",
  color: "white",
  alignment: "left",
});

const generateNumberedTextList = (lines = []) =>
  lines.map((line, index) => ({
    text: `${index + 1}. ${line}`,
    margin: [5, 5, 5, 5],
  }));

// Grand Total in Words
const numberToWords = (num) => {
  const converter = require("number-to-words");
  return converter.toWords(num).toUpperCase();
};

const generateGrandTotalWords = (amount) => [
  {
    text: `Amount in Words: ${numberToWords(amount)} ONLY`,
    margin: [5, 5, 5, 5],
    bold: true,
    fontSize: 9,
  },
];

// ---------- Sections ----------
const generateHeader = (type, tenantName, location) => ({
  columns: [
    { text: tenantName ? `${tenantName} - ${location}` : "", style: "header", width: "50%" },
    {
      text: type,
      style: "rightHeader",
      width: "50%",
      alignment: "right",
    },
  ],
  margin: [10, 10, 10, 10],
});

const buildTableRowsFromObj = (obj) =>
  Object.entries(obj)
    .filter(([key]) => key !== "type") 
    .map(([label, value]) => [
      { text: label, style: "labelStyle" },
      value,
    ]);

const generateSection = (title, obj, widths) => {
  const rows = buildTableRowsFromObj(obj);
  if (!rows.length) return null;

  return {
    header: generateHeaderName(title),
    content: {
      stack: [
        { table: { widths, body: rows }, layout: "noBorders" },
      ],
      margin: [5, 5, 5, 5],
    },
  };
};

const generateDetails = (primaryData, secondaryData = null) => {
  const sections = [
    generateSection(`${primaryData.type} Details`, primaryData, ["40%", "60%"]),
     secondaryData ? 
     generateSection(
      secondaryData.type ? `${secondaryData.type} Details` : '', 
      secondaryData, 
      ["40%", "60%"]
    )
    : null,
  ].filter(Boolean);

  const widths = sections.length === 1 ? ["100%"] : sections.map(() => "50%");

  return {
    table: {
      widths,
      body: [
        sections.map(sec => sec.header),
        sections.map(sec => sec.content),
      ],
    },
    layout: getBorderLayout(),
    margin: [0, 0, 0, 0],
  };
};

// ---------- Table ----------
const formatCell = (text, opts = {}) => ({
  text: text != null ? text.toString() : "",
  style: "cellStyle",
  fontSize: 8,
  alignment: opts.alignment || "center",
});

const formatHeader = (text) => ({
  text,
  style: "tableHeader",
  fillColor: "#5c8373",
  color: "white",
  fontSize: 8,
});

const formatNumber = (num, opts = {}) =>
  formatCell(truncateNumber(num), { alignment: opts.alignment || "right" });


const generateAmountInWordsRow = (amount, columnCount) => {
  const amountInWords = convertRupeesToWords(amount);

  const firstCell = {
    text: "Amount in Words:",
    colSpan: 2,
    alignment: "left",
    style: "grandTotalStyle",
    fontSize: 8,
    margin: [3, 5, 3, 5],
  };

  const spanCell = { _span: true };

  const wordsCell = {
    text: amountInWords,
    colSpan: columnCount - 2,
    alignment: "left",
    fontSize: 8,
    margin: [3, 5, 3, 5],
    border: [false, true, false, true],
  };

  // Fillers for remaining spanned columns (PDFMake requires placeholders)
  const fillerCells = Array(columnCount - 3).fill({ _span: true });

  return [firstCell, spanCell, wordsCell, ...fillerCells];
};


const generateItemsTable = (items) => {
  const freightCharge = 0;

  const itemRows = items.map((item, i) => [
    formatCell(i + 1),
    formatCell(item.itemName),
    formatCell(item.purchaseUOM),
    formatNumber(item.quantity),
    formatNumber(item.unitCost),
    formatNumber(item.disc || 0.0),
    formatNumber(item.cess || 0.0),
    formatNumber(item.taxRate),
    formatNumber(item.totalPrice - item.unitCost * item.quantity),
    formatNumber(item.totalPrice),
  ]);

  const grandTotal = items.reduce((sum, item) => sum + item.totalPrice, 0);
  const totalWithFreight = grandTotal + freightCharge;

  const totalRow = (label, value) => [
    { text: label, colSpan: 2, alignment: "left", style: "grandTotalStyle", fontSize: 8 },
    "",
    {}, {}, {}, {}, {}, {}, {},
    { text: value.toFixed(1), style: "grandTotalStyle", alignment: "right", fontSize: 8 },
  ];

  return {
    table: {
      headerRows: 1,
      widths: [20, "*", 40, 45, 45, 40, 40, 40, 45, 50],
      body: [
        [
          formatHeader("#"),
          formatHeader("Item Name"),
          formatHeader("Pkg"),
          formatHeader("Qty"),
          formatHeader("Price"),
          formatHeader("Disc"),
          formatHeader("Cess"),
          formatHeader("Tax%"),
          formatHeader("Tax Amt"),
          formatHeader("Total"),
        ],
        ...itemRows,
        totalRow("Total", grandTotal),
        totalRow("Freight/Transportation Charges", freightCharge),
        totalRow("Grand Total", totalWithFreight),
        generateAmountInWordsRow(totalWithFreight),
      ],
    },
    layout: getBorderLayout(),
    margin: [0, 0, 0, 0],
  };
};

const generateTransferTable = (type, items, dispatchItems = []) => {  
  
  if (type === 'Dispatch') {
    const itemRows = items.map((item, i) => {
      const pendingQty = item.requestedQuantity - item.dispatchedQuantity;
      return [
        formatCell(i + 1),
        formatCell(item.itemName),
        formatCell(item.itemCode),
        formatCell(item.countingUOM),
        formatNumber(item.requestedQuantity),
        // formatNumber(pendingQty),
        formatNumber(item.dispatchedQuantity),
        formatNumber(item.receivedQuantity),
      ];
    });

    return {
      table: {
        headerRows: 1,
        widths: [35, "*", 60, 50, 65, 60, 70],
        body: [
          [
            formatHeader("#"),
            formatHeader("Item Name"),
            formatHeader("Item Code"),
            formatHeader("UOM"),
            formatHeader("Requested Qty"),
            // formatHeader("Pending Qty"),
            formatHeader("Dispatched Qty"),
            formatHeader("Received Qty"),
          ],
          ...itemRows,
        ],
      },
      layout: getBorderLayout(),
      margin: [0, 0, 0, 0],
    };
  }

  if (type === 'Receive') {
    const itemRows = items.map((item, i) => {
      const dispatchItem = dispatchItems.find(
        (d) => d.itemId === item.itemId
      ) || {};

      return [
        formatCell(i + 1),
        formatCell(item.itemName),
        formatCell(item.itemCode),
        formatCell(item.countingUOM),
        formatNumber(dispatchItem.dispatchedQuantity),
        formatNumber(dispatchItem.receivedQuantity),
        formatNumber(dispatchItem.shortageQuantity),
      ];
    });

    return {
      table: {
        headerRows: 1,
        widths: [35, "*", 60, 50, 65, 60, 70],
        body: [
          [
            formatHeader("#"),
            formatHeader("Item Name"),
            formatHeader("Item Code"),
            formatHeader("UOM"),
            formatHeader("Dispatched Qty"),
            formatHeader("Received Qty"),
            formatHeader("Shortage Qty"),
          ],
          ...itemRows,
        ],
      },
      layout: getBorderLayout(),
      margin: [0, 0, 0, 0],
    };
  }
};

const generateTermsSection = (title, terms) => ({
  table: {
    widths: ["100%"],
    body: [[generateHeaderName(title)], [generateNumberedTextList(terms)]],
  },
  layout: getBorderLayout(),
  margin: [0, 0, 0, 0],
});

const generateRemarks = (remarks) => {
  return generateTermsSection("Remarks", remarks);
};

const generateCombinedTermsSection = (poTerms = [], paymentTerms = []) => {
  const combinedTerms = [...poTerms, ...paymentTerms];
  return generateTermsSection("PO & Payment Terms", combinedTerms);
};

const generateShipToAndSignature = (data) => ({
  table: {
    widths: ["60%", "40%"],
    body: [
      [generateHeaderName("Ship To"), generateHeaderName("Authorized Signature")],
      [
        {
          text: data?.address?.address
            ? [
                data.address.address + "\n",
                `${data.address.city || ""}, ${data.address.state || ""} - ${data.address.pincode || ""}`
              ]
            : data?.shipTo || "",
          margin: [5, 10, 5, 10],
          fontSize: 9,
        },
        {
          text: "\n\n\nAuthorized Signatory",
          alignment: "center",
          margin: [5, 20, 5, 5],
          fontSize: 9,
        },
      ],
    ],
  },
  layout: getBorderLayout(),
  margin: [0, 0, 0, 0],
});

const generateApprovalSignatures = () => {
  const createSignatureCell = (label) => ({
    text: `\n\n\n${label}`,
    alignment: "center",
    margin: [5, 20, 5, 5],
    fontSize: 9,
  });

  return {
    table: {
      widths: ["33.33%", "33.33%", "33.34%"],
      body: [
        [
          generateHeaderName("Created By"),
          generateHeaderName("Approved By"),
          generateHeaderName("Checked By")
        ],
        [
          createSignatureCell("Created By"),
          createSignatureCell("Approved By"),
          createSignatureCell("Checked By")
        ],
      ],
    },
    layout: getBorderLayout(),
    margin: [0, 0, 0, 0],
  };
};

const convertRupeesToWords = (amount) => {
  const ones = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
  const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];
  const teens = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
  const convertLessThanHundred = (n) => {
    if (n === 0) return '';
    if (n < 10) return ones[n];
    if (n < 20) return teens[n - 10];
    return tens[Math.floor(n / 10)] + (n % 10 ? ' ' + ones[n % 10] : '');
  };
  const convertLessThanThousand = (n) => {
    if (n === 0) return '';
    if (n < 100) return convertLessThanHundred(n);
    return ones[Math.floor(n / 100)] + ' Hundred' + (n % 100 ? ' ' + convertLessThanHundred(n % 100) : '');
  };
  const [rupees, paise] = amount.toFixed(2).split('.').map(Number);
  if (rupees === 0 && paise === 0) return 'Zero Rupees';
  const crore = Math.floor(rupees / 10000000);
  const lakh = Math.floor((rupees % 10000000) / 100000);
  const thousand = Math.floor((rupees % 100000) / 1000);
  const remainder = rupees % 1000;
  let result = '';
  if (crore) result += convertLessThanThousand(crore) + ' Crore ';
  if (lakh) result += convertLessThanThousand(lakh) + ' Lakh ';
  if (thousand) result += convertLessThanThousand(thousand) + ' Thousand ';
  if (remainder) result += convertLessThanThousand(remainder);
  result = result.trim() + ' Rupees';
  if (paise > 0) {
    result += ' and ' + convertLessThanHundred(paise) + ' Paise';
  }
  return result;
};

// const generateDynamicTable = (config) => {
//   const {
//     items,
//     columns,
//     totalRows = [],
//     specialRows = [],
//     tableOptions = {},
//   } = config;

//   // Generate item rows dynamically based on column configuration
//   const itemRows = items.map((item, index) =>
//     columns.map((col) => {
//       if (col.key === 'index') {
//         return formatCell(index + 1);
//       }
      
//       const value = col.getValue ? col.getValue(item, index) : item[col.key];
//       return col.format ? col.format(value) : formatCell(value);
//     })
//   );

//   // Generate header row from columns
//   const headerRow = columns.map((col) => formatHeader(col.header));

//   // Calculate totals
//   const calculatedTotals = totalRows.map((totalConfig) => {
//     const value = totalConfig.calculate 
//       ? totalConfig.calculate(items)
//       : totalConfig.value || 0;

//     return createTotalRow(totalConfig.label, value, columns.length);
//   });

//   // Combine all rows
//   const bodyRows = [
//     headerRow,
//     ...itemRows,
//     ...calculatedTotals,
//     ...specialRows.map((row) => row(columns.length)),
//   ];

//   return {
//     table: {
//       headerRows: 1,
//       widths: columns.map((col) => col.width || '*'),
//       body: bodyRows,
//     },
//     layout: tableOptions.layout || getBorderLayout(),
//     margin: tableOptions.margin || [0, 0, 0, 0],
//   };
// };

// // Helper function for total rows
// const createTotalRow = (label, value, columnCount) => {
//   const row = Array(columnCount).fill({});
//   row[0] = {
//     text: label,
//     colSpan: 2,
//     alignment: 'left',
//     style: 'grandTotalStyle',
//     fontSize: 8,
//   };
//   row[1] = '';
//   row[columnCount - 1] = {
//     text: typeof value === 'number' ? value.toFixed(1) : value,
//     style: 'grandTotalStyle',
//     alignment: 'right',
//     fontSize: 8,
//   };
//   return row;
// };


const generateDynamicTable = (config) => {
  const {
    items,
    columns,
    totalRows = [],
    specialRows = [],
    tableOptions = {},
  } = config;

  const itemRows = items.map((item, index) =>
    columns.map((col) => {
      if (col.key === 'index') {
        return formatCell(index + 1);
      }
      
      const value = col.getValue ? col.getValue(item, index) : item[col.key];
      return col.format ? col.format(value) : formatCell(value);
    })
  );

  const headerRow = columns.map((col) => formatHeader(col.header));

  // CHANGED: Support column-specific values
  const calculatedTotals = totalRows.map((totalConfig) => {
    if (totalConfig.values) {
      return createTotalRowWithColumns(totalConfig.label, totalConfig.values, columns, items);
    }
    
    const value = totalConfig.calculate 
      ? totalConfig.calculate(items)
      : totalConfig.value || 0;

    return createTotalRow(totalConfig.label, value, columns.length);
  });

  const bodyRows = [
    headerRow,
    ...itemRows,
    ...calculatedTotals,
    ...specialRows.map((row) => row(columns.length)),
  ];

  return {
    table: {
      headerRows: 1,
      widths: columns.map((col) => col.width || '*'),
      body: bodyRows,
    },
    layout: tableOptions.layout || getBorderLayout(),
    margin: tableOptions.margin || [0, 0, 0, 0],
  };
};

// NEW FUNCTION: Handle column-specific totals
const createTotalRowWithColumns = (label, values, columns, items) => {
  // Find the first column index that has a value
  const firstValueIndex = columns.findIndex(col => values[col.key] !== undefined);
  const colSpan = firstValueIndex > 0 ? firstValueIndex : 2;

  return columns.map((col, index) => {
    // First cell with label spanning multiple columns
    if (index === 0) {
      return {
        text: label,
        colSpan: colSpan,
        alignment: 'left',
        style: 'grandTotalStyle',
        fontSize: 8,
      };
    }

    // Empty cells for the colspan range
    if (index < colSpan) {
      return '';
    }

    // Cells with values
    if (values[col.key] !== undefined) {
      const valueConfig = values[col.key];
      const calculatedValue = typeof valueConfig === 'function' 
        ? valueConfig(items) 
        : valueConfig;
      
      const formattedValue = col.format 
        ? col.format(calculatedValue) 
        : (typeof calculatedValue === 'number' ? calculatedValue.toFixed(1) : calculatedValue);

      return {
        text: formattedValue,
        style: 'grandTotalStyle',
        alignment: 'right',
        fontSize: 8,
      };
    }

    // Empty cells for columns without values
    return { text: '', style: 'grandTotalStyle', fontSize: 8 };
  });
};

// Keep existing createTotalRow for backward compatibility
const createTotalRow = (label, value, columnCount) => {
  const row = Array(columnCount).fill({});
  row[0] = {
    text: label,
    colSpan: 2,
    alignment: 'left',
    style: 'grandTotalStyle',
    fontSize: 8,
  };
  row[1] = '';
  row[columnCount - 1] = {
    text: typeof value === 'number' ? value.toFixed(1) : value,
    style: 'grandTotalStyle',
    alignment: 'right',
    fontSize: 8,
  };
  return row;
};
module.exports = {
  generatePrint,
  generateGrandTotalWords,
  generateHeader,
  generateDetails,
  generateItemsTable,
  generateDynamicTable,
  generateTransferTable,
  generateRemarks,
  generateCombinedTermsSection,
  generateShipToAndSignature,
  generateApprovalSignatures,
  formatNumber,
  generateAmountInWordsRow
};
