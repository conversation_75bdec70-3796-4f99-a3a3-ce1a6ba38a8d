/**
 * Report Handler Helper
 * ---------------------
 * Provides a reusable wrapper to create consistent Express handlers
 * for report endpoints across different modules.
 *
 * Responsibilities:
 * - Extract tenantId from route params.
 * - Extract payload and resultType from request body/query.
 * - Call the corresponding service function.
 * - Use the common render helper to send JSON or export response.
 */

const { render, ResultType } = require("@/helpers/render");

/**
 * Creates a standardized report handler.
 *
 * @param {Function} serviceFn - The service function to call for this report.
 * @returns {Function} Express-compatible async handler.
 */
function createReportHandler(id, serviceFn) {
    return async (req, res) => {
        try {
            const { tenantId } = req.params;
            const { resultType = ResultType.JSON } = req.query;
            const payload = req.body;

            const reportData = await serviceFn(tenantId, payload, resultType);

            render(res, resultType, reportData, id);
        } catch (err) {
            console.error("[Report Handler Error]:", err);
            res.status(500).json({ error: err.message });
        }
    };
}

module.exports = { createReportHandler };
