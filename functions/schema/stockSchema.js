const Joi = require("joi");

/**
 * StockTable (Current Stock Snapshot)
 * ----------------------------------
 * One record per item + inventoryLocation
 * 
 * Tracks stock in "countingUOM" (the way items are physically stored/handled).
 * Also provides conversion to "recipeUOM" (the way items are consumed in recipes).
 */
const stockSchema = Joi.object({
  tenantId: Joi.string().required(),

  locationId: Joi.string().required(),
  locationName: Joi.string().required(),

  inventoryLocationId: Joi.string().required(),
  inventoryLocationName: Joi.string().required(),

  itemId: Joi.string().required(),
  itemCode: Joi.string().required(),
  itemName: Joi.string().required(),

  /**
   * 🔹 Unit of Measure
   * countingUOM: how the stock is physically managed (Bottle, Case, KG, Litre).
   * recipeUOM: how it is consumed in recipes (Gram, ML, etc).
   * conversionFactor: number of recipeUOM units in 1 countingUOM.
   */
  countingUOM: Joi.string().required(),
  recipeUOM: Joi.string().required(),
  conversionFactor: Joi.number().precision(4).required(), // e.g. 1 Bottle = 330 ML → 330

  /**
   * 🔹 Stock Balance
   * Always tracked in countingUOM.
   */
  qty: Joi.number().min(0).required(),

  /**
   * 🔹 Costing
   * avgCost → cost per countingUOM.
   * totalValue → total cost across all batches.
   */
  avgCost: Joi.number().precision(4).required(),
  totalValue: Joi.number().precision(2).required(),

  // @add package level information {-default, .. other package}

  // Optional: par level in countingUOM
  parLevel: Joi.number().min(0),

  CreatedAt: Joi.date().default(() => FD.now()),
  updatedAt: Joi.date().default(() => FD.now())
});

module.exports = { stockSchema };
