// schemas/grnSchema.js
const Joi = require("joi");

const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");

const attachmentSchema = require("@/models/attachmentSchema");
const vendorSchema = require("@/models/vendorSchema");
const { purchaseStatus } = require("@/defs/purchaseStatusDefs");
const { amountSchema } = require("@/models/amountSchema");

const userSchema = Joi.object({
  name: Joi.string().required(),
  id: Joi.string().required(),
});

const statusTimelineEntrySchema = Joi.object({
  name: Joi.string()
    .valid(
      purchaseStatus.COMPLETED,
      purchaseStatus.DELETED,
      purchaseStatus.RETURN_VENDOR
    )
    .required(),
  time: Joi.any().default(() => FD.now()),
  by: userSchema.required(),
});

const itemSchema = Joi.object({
  itemId: Joi.string().required(),
  itemName: Joi.string().required(),
  itemCode: Joi.string().required(),
  qtyOrdered: Joi.number().positive().required(),
  qtyReceived: Joi.number().positive().required(),
  purchaseUOM: Joi.string().required(),
  expiryDate: Joi.any().optional().allow(null),
  remarks: Joi.string().allow("", null),
  categoryId: Joi.string().required(),
  subcategoryId: Joi.string().required(),
  categoryName: Joi.string().required(),
  subcategoryName: Joi.string().required(),
  pkg: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().required(),
    packageCode: Joi.string().optional(),
    unitCost: Joi.number().optional(),
    quantity: Joi.number().positive().optional(),
  })
    .allow(null)
    .optional(),
  remarks: Joi.string().allow("", null),
  hsnCode: Joi.string().allow("", null),
  unitCost: Joi.number().precision(2).required(),
  foc: Joi.boolean().default(false),
  ...amountSchema,
});

/**
 * GRN Schema (Goods Received Note)
 * Joi validation schema for GRN summary document
 */

const grnSchema = Joi.object({
  tenantId: Joi.string().required(),

  grnNumber: Joi.string()
    .pattern(/^GRN\d+$/)
    .required()
    .description("Counter-based unique GRN identifier (e.g., GRN0001)"),

  grnDate: Joi.any().optional().allow(null).description("Date of the GRN"),

  // Invoice details
  invoiceDate: Joi.any()
    .optional()
    .allow(null)
    .description("Date of the invoice"),
  invoiceNumber: Joi.string()
    .optional()
    .allow(null, "")
    .description("Invoice number"),
  id: Joi.string().required(),

  // References
  poId: Joi.string()
    .optional()
    .allow(null, "")
    .description("Linked Purchase Order ID"),
  poNumber: Joi.string().optional().allow(null, "").description("PO number"),

  // vendor information
  vendorId: Joi.string().required().description("Vendor associated with GRN"),
  vendorName: Joi.string().required().description("Vendor name for reference"),
  vendor: vendorSchema.required(),

  location: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().required(),
  }).required(),
  inventoryLocation: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().required(),
  }).optional(),

  items: Joi.array().items(itemSchema).min(1).required(),
  status: Joi.string()
    .valid(
      purchaseStatus.COMPLETED,
      purchaseStatus.DELETED,
      purchaseStatus.RETURN_VENDOR
    )
    .required(),
  statusTimeline: Joi.array()
    .items(statusTimelineEntrySchema)
    .min(1)
    .optional(),
  // Metadata
  remarks: Joi.string()
    .optional()
    .allow(null, "")
    .description("General remarks"),

  attachments: Joi.array().items(attachmentSchema).default(null),

  createdBy: userSchema.required(),
  createdAt: Joi.any().default(() => FD.now()),
  updatedAt: Joi.any().default(() => FD.now()),
  removedBy: userSchema.optional(),
  removedAt: Joi.any().optional(),
  reason: Joi.string().optional().allow(null, ""),

  // Aggregated details
  // @todo: amt details
  ...amountSchema,
});

module.exports = grnSchema;
