const Joi = require('joi');

// Schema for sub-tax / components
const componentSchema = Joi.object({
  name: Joi.string().trim().required(),   // e.g., "SGST"
  value: Joi.number().min(0).required(),  // percentage
});

// Main tax schema
const taxSchema = Joi.object({
  tenantId: Joi.string().required(),
  name: Joi.string().trim().required(),        // e.g., "GST"
  nameNormalized: Joi.string().max(100).optional(),
  value: Joi.number().min(0).required(),       // main tax %
  activeStatus: Joi.boolean().default(true),   // true = active, false = inactive
  components: Joi.array().items(componentSchema).optional(), // optional sub-taxes
});

module.exports = taxSchema;
