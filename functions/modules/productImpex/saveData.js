const admin = require("firebase-admin");
const db = admin.firestore();
const { SHEET_NAMES } = require("./sheet");
const {
  getNextVendorId,
  // getNextInventoryItemId,
  // getNextRecipeId,
} = require("@/services/counterService");
const { COUNTER_TYPES } = require("@/defs/counterDefs");
const { DEFAULT_UNITS } = require("@/utils/defaultData");
const { rupeeToPaise, paiseToRupee } = require("@/utils/money");
const { COLLECTIONS } = require("@/defs/collectionDefs");

const vendorCollection = db.collection(COLLECTIONS.VENDORS);
const categoryCollection = db.collection(COLLECTIONS.CATEGORIES);
const tagCollection = db.collection(COLLECTIONS.TAGS);
const taxCollection = db.collection(COLLECTIONS.TAXES);
const houseUnitCollection = db.collection(COLLECTIONS.HOUSE_UNITS);
const inventoryItemCollection = db.collection(COLLECTIONS.INVENTORY_ITEMS);
const recipeCollection = db.collection(COLLECTIONS.RECEIPES);
const { getNextNumberRange } = require("@/repositories/counterRepo");
const { calculateRecipeCost } = require("@/utils/money");

const vendorSchema = require("@/models/vendorSchema");
const categorySchema = require("@/models/categorySchema");
const houseUnitSchema = require("@/models/houseUnitSchema");
const inventoryItemSchema = require("@/models/inventoryItemSchema");
const tagSchema = require("@/models/tagSchema");
const taxSchema = require("@/schema/taxSchema");
const { recipeSchema } = require("@/models/receipeSchema");

// General parser for Excel data
const parseData = (value, defaultValue = "") => {
  return value !== undefined && value !== null
    ? String(value).trim()
    : defaultValue;
};

const parseBool = (value) => {
  return parseData(value).toUpperCase() === "TRUE";
};

const parseActiveStatus = (value) => {
  return parseData(value).toUpperCase() === "ACTIVE";
};

const parseNumber = (value) => {
  return Number(parseData(value)) || 0;
};

const formatName = (name) => name?.trim().replace(/\s+/g, "").toLowerCase();

// Vendors
const saveVendors = async (tenantId, data = {}, meta = {}) => {
  const rows = data[SHEET_NAMES.VENDORS] || [];
  const errors = [];
  let successCount = 0;
  let failureCount = 0;

  // Load existing vendors into a map (normalized name → ref)
  const vendorDocs = await vendorCollection
    .where("tenantId", "==", tenantId)
    .get();
  const vendorMap = vendorDocs.docs.reduce((map, doc) => {
    map[formatName(doc.get("name"))] = doc.ref;
    return map;
  }, {});

  function formVendorData(row) {
    return {
      tenantId,
      name: parseData(row.name),
      nameNormalized: parseData(formatName(row.name)),
      vendorId: parseData(row.vendorId),
      contactName: parseData(row.contactName),
      contactNo: parseData(row.contactNo),
      contactEmailId: parseData(row.contactEmailId),
      address: {
        address: parseData(row.address),
        state: parseData(row.state),
        city: parseData(row.city),
        pincode: parseData(row.pincode),
      },
      cinNo: parseData(row.cinNo),
      gstNo: parseData(row.gstNo),
      panNo: parseData(row.panNo),
      tinNo: parseData(row.tinNo),
      poTerms: parseData(row.poTerms),
      paymentTerms: parseData(row.paymentTerms),
      activeStatus: parseActiveStatus(row.activeStatus),
    };
  }

  let batch = db.batch();
  let opCount = 0;

  async function commitBatch() {
    if (opCount === 0) return;
    await batch.commit();
    batch = db.batch();
    opCount = 0;
  }

  for (const [i, row] of rows.entries()) {
    const vendorData = formVendorData(row);

    if (!vendorData.vendorId) {
      vendorData.vendorId = await getNextVendorId(tenantId);
    }

    const docRef = vendorMap[vendorData.nameNormalized];

    // Validate vendorData using Joi
    const { error: validationError } = vendorSchema.validate(vendorData, {
      abortEarly: false,
    });

    if (validationError) {
      failureCount++;
      errors.push({
        rowNumber: i + 2,
        fields: [
          {
            name: vendorData.name || "Unknown Vendor",
            errors: validationError.details.map((d) => d.message),
          },
        ],
      });
      continue; // skip saving invalid row
    }

    // Proceed with saving valid row
    if (docRef) {
      batch.update(docRef, { id: docRef.id, ...vendorData });
    } else {
      const newDocRef = vendorCollection.doc();
      batch.set(newDocRef, { id: newDocRef.id, ...vendorData });
      vendorMap[vendorData.nameNormalized] = newDocRef;
    }

    opCount++;
    successCount++;

    if (opCount >= 500) {
      await commitBatch();
    }
  }

  await commitBatch();

  const logEntry = {
    errors,
    totalRecords: rows.length,
    successCount,
    failureCount,
    sheetName: "Vendors",
  };

  if (errors.length) {
    console.error("Vendor import errors:", JSON.stringify(errors, null, 2));
  }

  return {
    logEntry,
  };
};

// Categories
const saveCategories = async (tenantId, data = {}) => {
  const categories = data[SHEET_NAMES.CATEGORIES] || [];
  const subCategories = data[SHEET_NAMES.SUB_CATEGORIES] || [];
  const errors = [];
  let successCount = 0;
  let failureCount = 0;

  const categoryDocs = await categoryCollection
    .where("tenantId", "==", tenantId)
    .get();

  const categoryMap = categoryDocs.docs.reduce((map, doc) => {
    map[formatName(doc.get("name"))] = { ref: doc.ref, ...doc.data() };
    return map;
  }, {});

  const catMap = {};
  subCategories.forEach((res, index) => {
    const parent = parseData(res.category)?.toLowerCase();
    if (!parent) return;

    const subCatIns = { name: parseData(res.subCategory) };

    const catRef = categoryMap[formatName(res.category)];
    const subCatId = catRef?.subCategories?.find(
      (sub) => sub.name === subCatIns.name
    )?.id;

    subCatIns.id = subCatId || admin.firestore().collection("dummy").doc().id;

    if (!catMap[parent]) {
      catMap[parent] = [];
    }
    catMap[parent].push(subCatIns);
  });

  function formCategoryData(row) {
    const key = parseData(row.name)?.toLowerCase();
    return {
      tenantId,
      name: parseData(row.name),
      nameNormalized: parseData(formatName(row.name)),
      subCategories: catMap[key] || [],
      activeStatus: parseActiveStatus(row.activeStatus),
    };
  }

  let batch = db.batch();
  let opCount = 0;

  async function commitBatch() {
    if (opCount === 0) return;
    await batch.commit();
    batch = db.batch();
    opCount = 0;
  }

  for (const [i, row] of categories.entries()) {
    const categoryData = formCategoryData(row);

    const { error: categoryValidationError } = categorySchema.validate(
      categoryData,
      {
        abortEarly: false,
      }
    );

    if (categoryValidationError) {
      failureCount++;
      // Check if subcategory errors exist
      const subCatErrors = categoryValidationError.details.filter((d) =>
        d.path.includes("subCategories")
      );

      errors.push({
        rowNumber: i + 2,
        categoryName: categoryData.name || "Unknown Category",
        categoryErrors: categoryValidationError.details
          .filter((d) => !d.path.includes("subCategories"))
          .map((d) => d.message),
        subCategoryErrors: subCatErrors.map((d) => d.message),
      });
      continue;
    }

    const docRef = categoryMap[categoryData.nameNormalized];

    if (docRef) {
      batch.update(docRef.ref, { id: docRef.id, ...categoryData });
    } else {
      const newDocRef = categoryCollection.doc();
      batch.set(newDocRef, { id: newDocRef.id, ...categoryData });
      categoryMap[categoryData.nameNormalized] = { ref: newDocRef };
    }

    opCount++;
    successCount++;
    if (opCount >= 500) {
      await commitBatch();
    }
  }

  await commitBatch();

  const logEntry = {
    errors,
    totalRecords: categories.length,
    successCount,
    failureCount,
    sheetName: "Categories",
  };

  if (errors.length) {
    console.error(
      "Category/Subcategory import errors:",
      JSON.stringify(errors, null, 2)
    );
  }

  return { logEntry };
};

// Tags
const saveTags = async (tenantId, data = {}) => {
  const tags = data[SHEET_NAMES.TAGS] || [];
  const errors = [];
  let successCount = 0;
  let failureCount = 0;

  // Load existing tags into a map (normalized name → ref)
  const tagDocs = await tagCollection.where("tenantId", "==", tenantId).get();

  const tagMap = tagDocs.docs.reduce((map, doc) => {
    const normalized = formatName(doc.get("name"));
    map[normalized] = doc.ref;
    return map;
  }, {});

  function formTagData(row) {
    return {
      tenantId,
      name: parseData(row.name),
      activeStatus: parseActiveStatus(row.activeStatus),
    };
  }

  let batch = db.batch();
  let opCount = 0; // track operations

  async function commitBatch() {
    if (opCount === 0) return; // nothing to commit
    await batch.commit();
    batch = db.batch();
    opCount = 0;
  }

  const seenNames = new Set();

  for (const [i, row] of tags.entries()) {
    const tagData = formTagData(row);
    const normalizedName = formatName(tagData.name);
    const docRef = tagMap[normalizedName];
    const allErrors = [];

    // 1️⃣ Check if name is duplicated within import file
    if (seenNames.has(normalizedName)) {
      allErrors.push(
        `Tag '${tagData.name}' is duplicated in this import file.`
      );
    } else {
      seenNames.add(normalizedName);
    }

    // 2️⃣ Validate structure via Joi
    const { error: validationError } = tagSchema.validate(tagData, {
      abortEarly: false,
    });
    if (validationError) {
      allErrors.push(...validationError.details.map((d) => d.message));
    }

    if (!docRef && tagMap[normalizedName]) {
      allErrors.push(
        `Tag '${tagData.name}' already exists and cannot be created again.`
      );
    }

    if (allErrors.length > 0) {
      failureCount++;
      errors.push({
        rowNumber: i + 2,
        fields: [{ name: tagData.name || "Unknown Tag", errors: allErrors }],
      });
      continue;
    }

    if (docRef) {
      // Update existing tag
      batch.update(docRef, { id: docRef.id, ...tagData });
    } else {
      // Create new tag
      const newDocRef = tagCollection.doc();
      batch.set(newDocRef, { id: newDocRef.id, ...tagData });
      tagMap[normalizedName] = newDocRef;
    }

    opCount++;
    successCount++;
    if (opCount >= 500) await commitBatch();
  }

  // Commit leftover ops
  await commitBatch();

  const logEntry = {
    errors,
    totalRecords: tags.length,
    successCount,
    failureCount,
    sheetName: "Tag",
  };

  if (errors.length) {
    console.error("Tags import errors:", JSON.stringify(errors, null, 2));
  }

  return { logEntry };
};

// Taxes
const saveTaxes = async (tenantId, data = {}) => {
  const taxes = data[SHEET_NAMES.TAXES] || [];
  const errors = [];
  let successCount = 0;
  let failureCount = 0;

  // 🔹 Load existing taxes from DB into a map (normalized name → ref)
  const taxDocs = await taxCollection.where("tenantId", "==", tenantId).get();
  const taxMap = taxDocs.docs.reduce((map, doc) => {
    const normalized = formatName(doc.get("name")); // e.g., "gst"
    map[normalized] = doc.ref;
    return map;
  }, {});

  // 🔹 Track names encountered in current import to detect duplicates within same file
  const importedNames = new Set();

  function parseComponentsToArray(components) {
    if (!components) return [];
    return components.split(",").map((component) => {
      const [name, value] = component.trim().split("|");
      return { name: parseData(name), value: parseNumber(value) };
    });
  }

  function formTaxData(row) {
    return {
      tenantId,
      name: parseData(row.name),
      value: parseNumber(row.value),
      nameNormalized: parseData(formatName(row.name)),
      components: parseComponentsToArray(row.components),
      activeStatus: parseActiveStatus(row.activeStatus),
    };
  }

  let batch = db.batch();
  let opCount = 0;

  async function commitBatch() {
    if (opCount === 0) return;
    await batch.commit();
    batch = db.batch();
    opCount = 0;
  }

  const seenNames = new Set(); // Track duplicates within import file

  for (const [i, row] of taxes.entries()) {
    const taxData = formTaxData(row);
    const normalizedName = formatName(taxData.name);
    const docRef = taxMap[normalizedName];
    const allErrors = [];

    // 1️⃣ Check if name is duplicated within import file
    if (seenNames.has(normalizedName)) {
      allErrors.push(
        `Tax '${taxData.name}' is duplicated in this import file.`
      );
    } else {
      seenNames.add(normalizedName);
    }

    // 2️⃣ Validate structure via Joi
    const { error: validationError } = taxSchema.validate(taxData, {
      abortEarly: false,
    });
    if (validationError) {
      allErrors.push(...validationError.details.map((d) => d.message));
    }

    // 3️⃣ Component validation
    const componentCount = taxData.components?.length || 0;
    if (componentCount === 1) {
      allErrors.push(
        "Components must be either empty or contain at least 2 items"
      );
    }

    if (componentCount >= 2) {
      const totalComponentValue = taxData.components.reduce(
        (sum, comp) => sum + (parseFloat(comp.value) || 0),
        0
      );
      const diff = Math.abs(
        totalComponentValue - parseFloat(taxData.value || 0)
      );
      if (diff > 0.001) {
        allErrors.push(
          `Sum of component values (${totalComponentValue}) must equal tax value (${taxData.value})`
        );
      }
    }

    if (!docRef && taxMap[normalizedName]) {
      allErrors.push(
        `Tax '${taxData.name}' already exists and cannot be created again.`
      );
    }

    if (allErrors.length > 0) {
      failureCount++;
      errors.push({
        rowNumber: i + 2,
        fields: [{ name: taxData.name || "Unknown Tax", errors: allErrors }],
      });
      continue;
    }

    if (docRef) {
      // Update existing tax
      batch.update(docRef, { id: docRef.id, ...taxData });
    } else {
      // Create new tax
      const newDocRef = taxCollection.doc();
      batch.set(newDocRef, { id: newDocRef.id, ...taxData });
      taxMap[normalizedName] = newDocRef;
    }

    opCount++;
    successCount++;
    if (opCount >= 500) await commitBatch();
  }

  await commitBatch();

  const logEntry = {
    errors,
    totalRecords: taxes.length,
    successCount,
    failureCount,
    sheetName: "Tax",
  };

  if (errors.length) {
    console.error("Tax import errors:", JSON.stringify(errors, null, 2));
  }

  return { logEntry };
};

// House Units
const saveHouseUnit = async (tenantId, data = {}) => {
  const houseUnits = data[SHEET_NAMES.HOUSE_UNITS] || [];
  const errors = [];
  let successCount = 0;
  let failureCount = 0;

  // Load existing house units into a map (normalized name → ref)
  const houseUnitDocs = await houseUnitCollection
    .where("tenantId", "==", tenantId)
    .select("nameNormalized")
    .get();

  const houseUnitMap = {};
  houseUnitDocs.forEach((doc) => {
    const nn = doc.get("nameNormalized");
    if (nn) houseUnitMap[nn] = doc.ref;
  });

  function formHouseUnitData(row) {
    return {
      tenantId,
      name: parseData(row.name),
      nameNormalized: parseData(formatName(row.name)),
      symbol: parseData(row.symbol).toLowerCase(),
      quantity: parseNumber(row.quantity),
      toUnit: parseData(row.toUnit).toLowerCase(),
      activeStatus: parseActiveStatus(row.activeStatus),
      default: false,
    };
  }

  let batch = db.batch();
  let opCount = 0; // track operations

  async function commitBatch() {
    if (opCount === 0) return; // nothing to commit
    await batch.commit();
    batch = db.batch();
    opCount = 0;
  }

  for (const [i, row] of houseUnits.entries()) {
    const houseUnitData = formHouseUnitData(row);

    // Validate vendorData using Joi
    const { error: validationError } = houseUnitSchema.validate(houseUnitData, {
      abortEarly: false,
    });

    if (validationError) {
      failureCount++;
      errors.push({
        rowNumber: i + 2,
        fields: [
          {
            name: houseUnitData.name || "Unknown Unit",
            errors: validationError.details.map((d) => d.message),
          },
        ],
      });
      continue; // skip saving invalid row
    }

    const normalizedName = houseUnitData.nameNormalized;
    const docRef = houseUnitMap[normalizedName];

    if (docRef) {
      batch.update(docRef, { id: docRef.id, ...houseUnitData });
    } else {
      const newDocRef = houseUnitCollection.doc();
      batch.set(newDocRef, { id: newDocRef.id, ...houseUnitData });

      // 🔑 Add to vendorMap so duplicate rows in same import don't insert again
      houseUnitMap[normalizedName] = newDocRef;
    }

    opCount++;
    successCount++;
    if (opCount >= 500) {
      await commitBatch();
    }
  }
  await commitBatch();

  const logEntry = {
    errors,
    totalRecords: houseUnits.length,
    successCount,
    failureCount,
    sheetName: "House Unit",
  };

  if (errors.length) {
    console.error("HouseUnit import errors:", JSON.stringify(errors, null, 2));
  }
  return {
    logEntry,
  };
};

// Inventory Items
const saveInventoryItems = async (tenantId, data = {}) => {
  const inventoryItems = data[SHEET_NAMES.INVENTORY_ITEMS] || [];
  const inventoryPackages = data[SHEET_NAMES.INVENTORY_PACKAGES] || [];
  const inventoryIngredients = data[SHEET_NAMES.INVENTORY_INGREDIENTS] || [];
  const errors = [];
  let successCount = 0;
  let failureCount = 0;

  const [
    inventoryItemDocs,
    categoryDocs,
    houseUnitDocs,
    vendorDocs,
    tagDocs,
    taxDocs,
  ] = await Promise.all([
    inventoryItemCollection.where("tenantId", "==", tenantId).get(),
    categoryCollection.where("tenantId", "==", tenantId).get(),
    houseUnitCollection.where("tenantId", "==", tenantId).get(),
    vendorCollection.where("tenantId", "==", tenantId).get(),
    tagCollection.where("tenantId", "==", tenantId).get(),
    taxCollection.where("tenantId", "==", tenantId).get(),
  ]);

  const inventoryItemMap = inventoryItemDocs.docs.reduce((map, doc) => {
    map[formatName(doc.get("itemName"))] = { ref: doc.ref, ...doc.data() };
    return map;
  }, {});

  const categoryMap = categoryDocs.docs.reduce((map, doc) => {
    map[formatName(doc.get("name"))] = doc.data();
    return map;
  }, {});

  const houseUnitDocsData = [
    ...houseUnitDocs.docs.map((doc) => doc.data()),
    ...DEFAULT_UNITS,
  ];

  const houseUnitMap = houseUnitDocsData.reduce((map, data) => {
    map[parseData(data.symbol)] = data;
    return map;
  }, {});

  const vendorMap = vendorDocs.docs.reduce((map, doc) => {
    const vendor = doc.data();
    map[formatName(vendor.name)] = doc.data();
    return map;
  }, {});

  const tagMap = tagDocs.docs.reduce((map, doc) => {
    const tag = doc.data();
    map[formatName(tag.name)] = doc.data();
    return map;
  }, {});

  const taxMap = taxDocs.docs.reduce((map, doc) => {
    const tax = doc.data();
    map[formatName(tax.name)] = { id: doc.id, ...tax };
    return map;
  }, {});

  const findCategory = (category) => {
    return categoryMap[formatName(category)];
  };

  const unitMap = new Map();
  houseUnitDocsData.forEach((u) => unitMap.set(u.symbol, u));

  //  Function to calculate conversion from A to B (BFS)
  // const conversionCache = new Map();
  // function getConvertedQuantity(fromSymbol, toSymbol) {
  //   // identical units need no conversion
  //   if (fromSymbol === toSymbol) return 1;

  //   const cacheKey = `${fromSymbol}->${toSymbol}`;
  //   if (conversionCache.has(cacheKey)) return conversionCache.get(cacheKey);

  //   const visited = new Set();
  //   const queue = [{ symbol: fromSymbol, quantity: 1 }];
  //   let result = null;

  //   while (queue.length > 0) {
  //     const { symbol, quantity } = queue.shift();
  //     if (symbol === toSymbol) {
  //       result = quantity;
  //       break;
  //     }

  //     const unit = unitMap.get(symbol);
  //     if (!unit || !unit.conversions) continue;

  //     for (const conv of unit.conversions) {
  //       if (visited.has(conv.toUnit)) continue;
  //       visited.add(conv.toUnit);
  //       queue.push({ symbol: conv.toUnit, quantity: quantity * conv.quantity });
  //     }
  //   }

  //   conversionCache.set(cacheKey, result);
  //   return result;
  // }

  function getConversionChain(startSymbol) {
    // Internal: Convert array into a map for O(1) lookup
    const unitMap = {};
    houseUnitDocsData.forEach((u) => {
      unitMap[u.symbol] = u;
    });

    const chain = [];
    const visited = new Set();

    let current = startSymbol;

    while (current && !visited.has(current)) {
      visited.add(current);

      const unit = unitMap[current];
      if (!unit) break;

      chain.push(unit); // Add current unit to result chain
      current = unit.toUnit; // Move deeper (kg → g → mg)
    }

    return chain;
  }

  const findUnit = (symbol) => {
    return houseUnitMap[parseData(symbol)];
  };

  function parsePurchaseUnit(unit, rowIndex, itemName) {
    const unitData = findUnit(unit);

    if (!unitData) {
      errors.push({
        rowNumber: rowIndex ? rowIndex + 2 : null,
        fields: [
          {
            name: itemName || "Unknown Item",
            errors: [`Unit not found: ${parseData(unit)}`],
          },
        ],
      });

      return {
        id: null,
        name: parseData(unit),
        symbol: parseData(unit),
        toUnit: null,
        quantity: null,
      };
    }

    return {
      id: parseData(unitData.id),
      name: `${parseData(unitData.name)}`,
      symbol: parseData(unitData.symbol),
      quantity: parseNumber(unitData.quantity) || null,
      toUnit: parseData(unitData.toUnit) || null,
    };
  }

  function parseUnit(fromUnit, toUnit, rowIndex = null, itemName = null) {
    const fromSymbol = parseData(fromUnit) || "unknown";
    const toSymbol = parseData(toUnit) || "unknown";
    const unitData = findUnit(toSymbol);

    // Helper to push errors cleanly
    const pushError = (msg) => {
      errors.push({
        rowNumber: rowIndex ? rowIndex + 2 : null,
        fields: [
          {
            name: itemName || "Unknown Item",
            errors: [msg],
          },
        ],
      });
    };

    if (fromSymbol === toSymbol) {
      if (!unitData) {
        pushError(`Unit not found: "${toSymbol}"`);
        return {
          id: null,
          name: toSymbol,
          symbol: toSymbol,
          quantity: null,
          toUnit: null,
        };
      }

      return {
        id: parseData(unitData.id),
        name: parseData(unitData.name),
        symbol: parseData(unitData.symbol),
        quantity: 1,
        toUnit: toSymbol,
      };
    }

    // 2️⃣ Target unit exist
    if (!unitData) {
      pushError(`Unit not found: ${toSymbol}`);
      return {
        id: null,
        name: toSymbol,
        symbol: toSymbol,
        quantity: null,
        toUnit: null,
      };
    }
    // Fetch full conversion chain from the starting symbol
    const chain = getConversionChain(fromSymbol);

    // Find matching conversion
    const conversion = chain.find((u) => u.toUnit === toSymbol);

    if (!conversion) {
      pushError(
        `Unit conversion not found: from "${fromSymbol}" to "${toSymbol}"`
      );

      return {
        id: null,
        name: toSymbol,
        symbol: toSymbol,
        quantity: null,
        toUnit: null,
      };
    }

    return {
      id: parseData(unitData.id),
      name: `${parseData(unitData.name)}`,
      symbol: parseData(unitData.symbol),
      quantity: parseNumber(conversion.quantity),
      toUnit: toSymbol,
    };
  }

  function parseVendors(vendorNames, rowIndex, itemName) {
    const raw = parseData(vendorNames);
    if (!raw) return [];

    const vendors = raw
      .split(",")
      .map((v) => v.trim())
      .filter(Boolean);

    const vendorList = [];
    const missingVendors = [];

    vendors.forEach((v) => {
      const vendor = vendorMap[formatName(v)];
      if (vendor) {
        vendorList.push({ id: vendor.id, name: parseData(vendor.name) });
      } else {
        missingVendors.push(v);
      }
    });

    if (missingVendors.length > 0) {
      errors.push({
        rowNumber: rowIndex + 2,
        fields: [
          {
            name: itemName || "Unknown Item",
            errors: [`Vendor(s) not found: ${missingVendors.join(", ")}`],
          },
        ],
      });
    }

    return vendorList;
  }

  function parseTags(tagName) {
    const raw = parseData(tagName);

    // if empty string or null → return []
    if (!raw) return [];

    // split by comma and trim spaces
    const tags = raw
      .split(",")
      .map((v) => v.trim())
      .filter(Boolean);

    return tags
      .map((v) => {
        const tag = tagMap[formatName(v)];
        return { id: tag?.id, name: parseData(tag?.name) };
      })
      .filter(Boolean); // remove nulls if tag not found
  }

  function parseTaxes(taxNames) {
    const raw = parseData(taxNames);

    // if empty string or null → return []
    if (!raw) return [];

    // split by comma and trim spaces
    const taxes = raw
      .split(",")
      .map((v) => v.trim())
      .filter(Boolean);

    return taxes
      .map((v) => {
        const tax = taxMap[formatName(v)];
        return { id: tax.id, name: parseData(tax.name) };
      })
      .filter(Boolean); // remove nulls if tax not found
  }

  const packageMap = {};
  inventoryPackages.forEach((res) => {
    const parent = parseData(res.itemName)?.toLowerCase();
    if (!parent) return;

    const packageIns = {
      name: parseData(res.name),
      id: inventoryItemCollection.doc().id,
      packageCode: parseData(res.packageCode),
      quantity: parseNumber(res.quantity),
      unitCost: parseNumber(res.unitCost) ? rupeeToPaise(res.unitCost) : 0,
      emptyWeight: parseNumber(res.emptyWeight) || 0,
      fullWeight: parseNumber(res.fullWeight) || 0,
    };

    if (!packageMap[parent]) {
      packageMap[parent] = [];
    }
    packageMap[parent].push(packageIns);
  });

  const ingredientMap = {};
  inventoryIngredients.forEach((res) => {
    const parent = parseData(res.itemName)?.toLowerCase();
    if (!parent) return;

    const ingredientRef = inventoryItemMap[formatName(res.ingItemName)];
    if (!ingredientRef) return;

    const ingredientIns = {
      itemId: parseData(ingredientRef.id),
      itemName: parseData(ingredientRef.itemName),
      itemCode: parseData(ingredientRef.itemCode),
      quantity: parseNumber(res.quantity),
      unitCost: parseNumber(ingredientRef.unitCost),
      purchaseUnit: parsePurchaseUnit(
        ingredientRef.purchaseUnit.symbol.toLowerCase()
      ),
      countingUnit: parseUnit(
        ingredientRef.purchaseUnit.symbol.toLowerCase(),
        ingredientRef.countingUnit.symbol.toLowerCase()
      ),
      recipeUnit: parseUnit(
        ingredientRef.countingUnit.symbol.toLowerCase(),
        ingredientRef.recipeUnit.symbol.toLowerCase()
      ),
    };

    if (!ingredientMap[parent]) {
      ingredientMap[parent] = [];
    }
    ingredientMap[parent].push(ingredientIns);
  });

  function formInventoryData(row, rowIndex) {
    console.log(row);
    const category = findCategory(row.category);
    const categoryData = {
      id: category.id,
      name: parseData(category.name),
    };
    const subCategoryData = category.subCategories.find(
      (sub) =>
        parseData(sub.name).toLowerCase() ===
        parseData(row.subCategory).toLowerCase()
    );
    const result = {
      tenantId,
      itemName: parseData(row.itemName),
      nameNormalized: parseData(formatName(row.itemName)),
      itemType: parseData(row.itemType),
      itemCode: parseData(row.itemCode),
      category: categoryData,
      subCategory: subCategoryData,
      tags: parseTags(row.tags),
      taxes: parseTaxes(row.taxes),
      purchaseUnit: parsePurchaseUnit(
        row.purchaseUnit.toLowerCase(),
        rowIndex,
        row.itemName
      ),
      countingUnit: parseUnit(
        row.purchaseUnit.toLowerCase(),
        row.countingUnit.toLowerCase(),
        rowIndex,
        row.itemName
      ),
      recipeUnit: parseUnit(
        row.countingUnit.toLowerCase(),
        row.recipeUnit.toLowerCase(),
        rowIndex,
        row.itemName
      ),
      parLevel: parseNumber(row.parLevel),
      trackExpiry: parseBool(row.trackExpiry),
      stockable: parseBool(row.stockable),
      vendors: parseVendors(row.vendors, rowIndex, row.itemName),
      prepareQuantity: parseNumber(row.prepareQuantity),
      unitCost: parseNumber(row.unitCost) ? rupeeToPaise(row.unitCost) : 0,
      activeStatus: parseActiveStatus(row.activeStatus),
      packages: packageMap[parseData(row.itemName).toLowerCase()] || [],
      ingredients: ingredientMap[parseData(row.itemName).toLowerCase()] || [],
      hsnCode: parseData(row.hsnCode),
      ledger: parseData(row.ledger),
      defaultPackage: parseBool(
        row.defaultPackage == null ? true : row.defaultPackage
      ),
    };

    return {
      ...result,
      showPackage: result.packages.length > 0,
    };
  }

  let batch = db.batch();
  let opCount = 0; // track operations

  // async function commitBatch() {
  //   if (opCount === 0) return; // nothing to commit
  //   await batch.commit();
  //   batch = db.batch();
  //   opCount = 0;
  // }
  const batchPromises = [];

  const totalItemsWithoutCode = inventoryItems.filter(
    (i) => !i.itemCode
  ).length;
  let idRange = null;
  if (totalItemsWithoutCode > 0) {
    idRange = await getNextNumberRange(
      tenantId,
      COUNTER_TYPES.INVENTORY_ITEM.key,
      COUNTER_TYPES.INVENTORY_ITEM.prefix,
      totalItemsWithoutCode
    );
  }

  let currentNumber = idRange ? idRange.start : null;

  for (const [i, row] of inventoryItems.entries()) {
    const inventoryData = formInventoryData(row, i); // pass row index

    // Auto-generate itemCode if missing
    if (!inventoryData.itemCode) {
      const formatted = String(currentNumber).padStart(4, "0");
      inventoryData.itemCode = `${idRange.prefix}${formatted}`;
      currentNumber++;
    }

    // ✅ Validate using Joi schema
    const { error: validationError } = inventoryItemSchema.validate(
      inventoryData,
      {
        abortEarly: false,
      }
    );

    if (validationError) {
      failureCount++;
      errors.push({
        rowNumber: i + 2,
        fields: [
          {
            name: inventoryData.itemName || "Unknown Item",
            errors: validationError.details.map((d) => d.message),
          },
        ],
      });
      continue;
    }

    // ✅ Proceed to add/update valid document
    const docRef = inventoryItemMap[inventoryData.nameNormalized];
    if (docRef) {
      batch.update(docRef.ref, { id: docRef.ref.id, ...inventoryData });
    } else {
      const newDocRef = inventoryItemCollection.doc();
      batch.set(newDocRef, { id: newDocRef.id, ...inventoryData });
      inventoryItemMap[inventoryData.nameNormalized] = { ref: newDocRef };
    }

    opCount++;
    if (opCount >= 500) {
      batchPromises.push(batch.commit());
      batch = db.batch();
      opCount = 0;
    }

    successCount++;
  }

  // Commit leftover ops
  if (opCount) batchPromises.push(batch.commit());
  await Promise.all(batchPromises);

  if (errors.length) {
    console.error(
      "Inventory item import errors:",
      JSON.stringify(errors, null, 2)
    );
  }

  const logEntry = {
    errors,
    totalRecords: inventoryItems.length,
    successCount,
    failureCount,
    sheetName: "Inventory Items",
  };

  return { logEntry };
};

// Recipes
const saveRecipe = async (tenantId, data = {}) => {
  const recipes = data[SHEET_NAMES.RECIPES] || [];
  const recipesIngredients = data[SHEET_NAMES.RECIPE_INGREDIENTS] || [];
  const errors = [];
  let successCount = 0;
  let failureCount = 0;

  // Step 1: Parallel fetch base data
  const [recipeDocs, tagDocs, inventoryItemDocs, houseUnitDocs] =
    await Promise.all([
      recipeCollection.where("tenantId", "==", tenantId).get(),
      tagCollection.where("tenantId", "==", tenantId).get(),
      inventoryItemCollection.where("tenantId", "==", tenantId).get(),
      houseUnitCollection.where("tenantId", "==", tenantId).get(),
    ]);

  // Step 2: Build lookup maps
  const recipeMap = recipeDocs.docs.reduce((map, doc) => {
    map[formatName(doc.get("name"))] = { ref: doc.ref, ...doc.data() };
    return map;
  }, {});

  const tagMap = tagDocs.docs.reduce((map, doc) => {
    const tag = doc.data();
    map[formatName(tag.name)] = doc.data();
    return map;
  }, {});

  const inventoryItemMap = inventoryItemDocs.docs.reduce((map, doc) => {
    const item = doc.data();
    map[formatName(doc.get("itemName"))] = {
      ref: doc.ref,
      ...item,
      unitCost: paiseToRupee(item.unitCost) || 0,
    };
    return map;
  }, {});

  const houseUnitDocsData = [
    ...houseUnitDocs.docs.map((doc) => doc.data()),
    ...DEFAULT_UNITS,
  ];

  const houseUnitMap = houseUnitDocsData.reduce((map, data) => {
    map[parseData(data.symbol)] = data;
    return map;
  }, {});

  const unitMap = new Map();
  houseUnitDocsData.forEach((u) => unitMap.set(u.symbol, u));

  // Step 3: Cached conversions
  const conversionCache = new Map();

  function getConvertedQuantity(fromSymbol, toSymbol) {
    if (fromSymbol === toSymbol) return 1;

    const cacheKey = `${fromSymbol}->${toSymbol}`;
    if (conversionCache.has(cacheKey)) return conversionCache.get(cacheKey);

    const visited = new Set();
    const queue = [{ symbol: fromSymbol, quantity: 1 }];
    let result = null;

    while (queue.length > 0) {
      const { symbol, quantity } = queue.shift();
      if (symbol === toSymbol) {
        result = quantity;
        break;
      }

      const unit = unitMap.get(symbol);
      if (!unit || !unit.conversions) continue;

      for (const conv of unit.conversions) {
        if (visited.has(conv.toUnit)) continue;
        visited.add(conv.toUnit);
        queue.push({ symbol: conv.toUnit, quantity: quantity * conv.quantity });
      }
    }

    result = result || 1;

    conversionCache.set(cacheKey, result);
    return result;
  }

  // Step 4: Utility helpers
  const findUnit = (symbol) => {
    return houseUnitMap[parseData(symbol)];
  };

  function parsePurchaseUnit(unit, rowIndex, itemName) {
    const unitData = findUnit(unit);

    if (!unitData) {
      errors.push({
        rowNumber: rowIndex ? rowIndex + 2 : null,
        fields: [
          {
            name: itemName || "Unknown Item",
            errors: [`Unit not found for conversion: ${parseData(unit)}"`],
          },
        ],
      });

      return {
        id: null,
        name: parseData(unit),
        symbol: parseData(unit),
      };
    }

    return {
      id: parseData(unitData.id),
      name: `${parseData(unitData.name)}`,
      symbol: parseData(unitData.symbol),
    };
  }

  function parseUnit(fromUnit, toUnit, rowIndex = null, itemName = null) {
    const unitData = findUnit(toUnit);

    if (!unitData) {
      const fromSymbol = parseData(fromUnit) || "unknown";
      const toSymbol = parseData(toUnit) || "unknown";

      errors.push({
        rowNumber: rowIndex ? rowIndex + 2 : null,
        fields: [
          {
            name: itemName || "Unknown Item",
            errors: [
              `Unit not found for conversion: from "${fromSymbol}" to "${toSymbol}"`,
            ],
          },
        ],
      });

      return {
        name: parseData(toSymbol),
        symbol: parseData(toSymbol),
        conversion: null,
      };
    }

    return {
      name: `${parseData(unitData.name)}`,
      symbol: parseData(unitData.symbol),
      conversion: getConvertedQuantity(fromUnit, toUnit) || null,
    };
  }

  function parseTags(tagNames) {
    const raw = parseData(tagNames);
    if (!raw) return [];

    const tags = raw
      .split(",")
      .map((v) => v.trim())
      .filter(Boolean);

    return tags
      .map((v) => {
        const tag = tagMap[formatName(v)];
        if (!tag) return null;
        return { id: tag.id, name: parseData(tag.name) };
      })
      .filter(Boolean);
  }

  // Step 5: Prebuild ingredient map
  const ingredientMap = {};
  recipesIngredients.forEach((res, rowIndex) => {
    const parent = parseData(res.recipeName)?.toLowerCase();
    if (!parent) return;

    const ingredientRef = inventoryItemMap[formatName(res.itemName)];
    const subRecipeRef = recipeMap[formatName(res.itemName)];
    if (!ingredientRef && !subRecipeRef) return;

    let ingredientIns;
    if (ingredientRef) {
      const totalCost = calculateRecipeCost(
        {
          unitCost: ingredientRef.unitCost,
          recipeUnit: ingredientRef.recipeUnit,
        },
        res.quantity
      );
      ingredientIns = {
        itemId: parseData(ingredientRef.id),
        itemName: parseData(ingredientRef.itemName),
        itemCode: parseData(ingredientRef.itemCode),
        quantity: parseNumber(res.quantity),
        unitCost: parseNumber(ingredientRef.unitCost)
          ? rupeeToPaise(ingredientRef.unitCost)
          : 0,
        totalCost: rupeeToPaise(totalCost),
        purchaseUnit: parsePurchaseUnit(
          ingredientRef.purchaseUnit.symbol.toLowerCase(),
          rowIndex,
          itemName
        ),
        countingUnit: parseUnit(
          ingredientRef.purchaseUnit.symbol.toLowerCase(),
          ingredientRef.countingUnit.symbol.toLowerCase(),
          rowIndex,
          ingredientRef.itemName
        ),
        recipeUnit: parseUnit(
          ingredientRef.purchaseUnit.symbol.toLowerCase(),
          ingredientRef.recipeUnit.symbol.toLowerCase(),
          rowIndex,
          ingredientRef.itemName
        ),
      };
    } else if (subRecipeRef) {
      const unitCost = paiseToRupee(subRecipeRef.cost) / subRecipeRef.quantity;
      const totalCost = calculateRecipeCost(
        {
          unitCost: unitCost,
          recipeUnit: subRecipeRef.recipeUnit.toLowerCase(),
        },
        res.quantity
      );
      ingredientIns = {
        itemId: parseData(subRecipeRef.id),
        itemName: parseData(subRecipeRef.name),
        itemCode: parseData(subRecipeRef.recipeCode),
        quantity: parseNumber(res.quantity),
        unitCost: parseNumber(unitCost) ? rupeeToPaise(unitCost) : 0,
        totalCost: rupeeToPaise(totalCost),
        recipeUnit: parseData(subRecipeRef.recipeUnit.toLowerCase()),
        isSubRecipe: true,
      };
    }

    if (!ingredientMap[parent]) ingredientMap[parent] = [];
    ingredientMap[parent].push(ingredientIns);
  });

  // Step 6: Function to form recipe data
  function formRecipeData(row) {
    const recipeName = parseData(row.name);
    const ingredients = ingredientMap[recipeName?.toLowerCase()] || [];

    // Calculate total cost from linked ingredients
    const totalCostPaise = ingredients.reduce(
      (acc, i) => acc + (parseNumber(i.totalCost) || 0),
      0
    );

    const result = {
      tenantId,
      name: parseData(row.name),
      nameNormalized: parseData(formatName(row.name)),
      recipeType: parseData(row.recipeType),
      recipeUnit: parseData(row.recipeUnit.toLowerCase()),
      tags: parseTags(row.tags),
      recipeCode: parseData(row.recipeCode),
      quantity: parseNumber(row.quantity),
      cost: totalCostPaise,
      ingredients: ingredientMap[parseData(row.name).toLowerCase()] || [],
      activeStatus: parseActiveStatus(row.activeStatus),
    };
    const totalAmt = result.ingredients.reduce(
      (acc, i) => acc + i.totalCost,
      0
    );
    result.cost = parseNumber(totalAmt);
    return result;
  }

  // Step 7: Handle code generation
  const totalRecipesWithoutCode = recipes.filter((r) => !r.recipeCode).length;
  let idRange = null;
  if (totalRecipesWithoutCode > 0) {
    idRange = await getNextNumberRange(
      tenantId,
      COUNTER_TYPES.RECIPE.key,
      COUNTER_TYPES.RECIPE.prefix,
      totalRecipesWithoutCode
    );
  }

  let currentNumber = idRange ? idRange.start : null;

  // Step 8: Chunked import to prevent memory overflow
  const CHUNK_SIZE = 200;
  for (let i = 0; i < recipes.length; i += CHUNK_SIZE) {
    const chunk = recipes.slice(i, i + CHUNK_SIZE);
    const batchPromises = [];
    let batch = db.batch();
    let opCount = 0;

    for (const [index, row] of chunk.entries()) {
      const recipeData = formRecipeData(row);

      if (!recipeData.recipeCode && idRange) {
        const formatted = String(currentNumber).padStart(4, "0");
        recipeData.recipeCode = `${idRange.prefix}${formatted}`;
        currentNumber++;
      }

      // const validationErrors = validate(recipeData);
      // if (validationErrors.length > 0) {
      //   errors.push({
      //     rowIndex: i + index,
      //     name: recipeData.name,
      //     errors: validationErrors,
      //   });
      //   continue;
      // }

      const { error: validationError } = recipeSchema.validate(recipeData, {
        abortEarly: false, // show all errors, not just first one
        allowUnknown: true,
      });

      if (validationError) {
        failureCount++;
        errors.push({
          rowNumber: i + index + 2,
          fields: [
            {
              name: recipeData.name || "Unknown Receipe",
              errors: validationError.details.map((d) => d.message),
            },
          ],
        });
        continue;
      }

      const docRef = recipeMap[recipeData.nameNormalized];

      if (docRef) {
        console.log(recipeData, "recipeDataaaaa");
        batch.update(docRef.ref, { id: docRef.ref?.id, ...recipeData });
      } else {
        const newDocRef = recipeCollection.doc();
        batch.set(newDocRef, { id: newDocRef.id, ...recipeData });
        recipeMap[recipeData.nameNormalized] = { ref: newDocRef };
      }

      opCount++;
      if (opCount >= 500) {
        batchPromises.push(batch.commit());
        batch = db.batch();
        opCount = 0;
      }
      successCount++;
    }

    if (opCount) batchPromises.push(batch.commit());
    await Promise.all(batchPromises);

    console.log(
      `✅ Processed ${Math.min(i + CHUNK_SIZE, recipes.length)} of ${
        recipes.length
      }`
    );
  }

  const logEntry = {
    errors,
    totalRecords: recipes.length,
    successCount,
    failureCount,
    sheetName: "Receipes",
  };

  if (errors.length) {
    console.warn("⚠️ Recipe import errors:", JSON.stringify(errors, null, 2));
  }

  return { logEntry };
};

// example validation function
// @Todo: need to write schema specific validations
function validate(data) {
  const errors = [];

  // add more validation rules as needed
  return errors;
}

exports.saveData = async (sheetName, tenantId, data) => {
  switch (sheetName) {
    case SHEET_NAMES.TAGS:
      return await saveTags(tenantId, data);
    case SHEET_NAMES.TAXES:
      return await saveTaxes(tenantId, data);
    case SHEET_NAMES.VENDORS:
      return await saveVendors(tenantId, data);
    case SHEET_NAMES.CATEGORIES:
      return await saveCategories(tenantId, data);
    case SHEET_NAMES.HOUSE_UNITS:
      return await saveHouseUnit(tenantId, data);
    case SHEET_NAMES.INVENTORY_ITEMS:
      return await saveInventoryItems(tenantId, data);
    case SHEET_NAMES.RECIPES:
      return await saveRecipe(tenantId, data);
    default:
      throw new Error("Unsupported sheet name");
  }
};
