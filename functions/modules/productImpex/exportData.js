const ExcelJS = require("exceljs");
const { getHeaders, expandSheetNames } = require("./sheet");
const { getData } = require("./fetchData");

/**
 * Exports product configuration data to an Excel file
 *
 * @param {String} tenantId Tenant ID
 * @param {Boolean} isTemplate Whether to export template sheets or actual data
 * @param {String[]} sheetNames Array of sheet names to export (validated before calling)
 * @returns {Object} An object containing the generated workbook and the file name
 */
exports.exportProductConfiguration = async (
  tenantId,
  isTemplate,
  sheetNames
) => {
  const workbook = new ExcelJS.Workbook();

  for (const sheetName of sheetNames) {
    // Expand to include extended sheets (e.g., recipe → recipe & ingredient)
    const targetSheetNames = expandSheetNames(sheetName);

    // Fetch all related data once based on the main sheetName
    const allData = await (
      isTemplate ? 
      Promise.resolve({}) : 
      getData(sheetName, tenantId)
    );

    for (const target of targetSheetNames) {
      const headers = getHeaders(target) || [];
      const sheet = workbook.addWorksheet(target);

      // Apply headers as columns
      sheet.columns = headers;
      // Target-specific rows come from allData fetched using main sheet
      const targetRows = allData[target] || [];

      // Insert rows for this target sheet
      sheet.addRows(targetRows);
    }
  }

  // Normalize sheet names for filename (lowercase, joined by `_`)
  const sheetsPart =
    sheetNames.length === 1
      ? sheetNames[0].toLowerCase()
      : sheetNames.map((s) => s.toLowerCase()).join("_");

  // Add type (template or export)
  const typePart = isTemplate ? "template" : "export";

  const datePart = formatDateTimeLocal();
  const fileName = `Product_${sheetsPart}_${typePart}_${datePart}.xlsx`;

  return {
    workbook,
    fileName,
  };
};

// Format date to DD-MM-YYYY_HH:mm:ss am/pm
function formatDateTimeLocal(timeZone = "Asia/Kolkata") {
  return new Intl.DateTimeFormat("en-GB", {
    timeZone,
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  })
    .format(new Date())
    .replace(",", "")
    .replace(/\//g, "-")
    .replace(/:/g, "-")
    .toLowerCase()
    .replace(" ", "_");
}
