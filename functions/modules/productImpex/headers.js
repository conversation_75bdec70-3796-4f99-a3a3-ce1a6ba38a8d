// Column definitions for Excel import/export templates
// Each sheet configuration defines the header, key (field name), and column width

// Tag sheet columns
const tagColumns = [
  { header: "Tag Name", key: "name", width: 30 },
  { header: "Status", key: "activeStatus", width: 20 },
];

// Tax sheet columns
const taxColumns = [
  { header: "Tax Name", key: "name", width: 30 },
  { header: "Tax Value", key: "value", width: 30 },
  { header: "Sub Taxes", key: "components", width: 30 },
  { header: "Status", key: "activeStatus", width: 20 },
];

// Vendor sheet columns
const vendorColumns = [
  { header: "Vendor Name", key: "name", width: 30 },
  { header: "Vendor ID", key: "vendorId", width: 30 },
  { header: "Contact Name", key: "contactName", width: 30 },
  { header: "Contact Number", key: "contactNo", width: 30 },
  { header: "Contact Email", key: "contactEmailId", width: 30 },
  { header: "Address", key: "address", width: 30 },
  { header: "State", key: "state", width: 30 },
  { header: "City", key: "city", width: 30 },
  { header: "Pincode", key: "pincode", width: 20 },
  { header: "CIN", key: "cinNo", width: 30 },
  { header: "GST", key: "gstNo", width: 30 },
  { header: "PAN", key: "panNo", width: 30 },
  { header: "TIN", key: "tinNo", width: 30 },
  { header: "PO Terms", key: "poTerms", width: 20 },
  { header: "Payment Terms", key: "paymentTerms", width: 20 },
  { header: "Status", key: "activeStatus", width: 20 },
];

// Category sheet columns
const categoryColumns = [
  { header: "Category Name", key: "name", width: 30 },
  { header: "Status", key: "activeStatus", width: 30 },
];

// Sub-category sheet columns
const subCategoryColumns = [
  { header: "Category Name", key: "category", width: 30 },
  { header: "Sub Category Name", key: "subCategory", width: 30 },
];

// House unit sheet columns
const houseUnitColumns = [
  { header: "Unit Name", key: "name", width: 30 },
  { header: "Unit Symbol", key: "symbol", width: 30 },
  { header: "Quantity", key: "quantity", width: 30 },
  { header: "To Unit", key: "toUnit", width: 30 },
  { header: "Status", key: "activeStatus", width: 30 },
];

// Inventory items sheet columns
const inventoryItemsColumns = [
  { header: "Item Name", key: "itemName", width: 30 },
  { header: "Item Code", key: "itemCode", width: 30 },
  { header: "Item Type", key: "itemType", width: 30 },
  { header: "Category", key: "category", width: 30 },
  { header: "Sub Category", key: "subCategory", width: 30 },
  { header: "Tags", key: "tags", width: 30 },
  { header: "Taxes", key: "taxes", width: 30 },
  { header: "Vendors", key: "vendors", width: 30 },
  { header: "Purchase Unit", key: "purchaseUnit", width: 30 },
  { header: "Counting Unit", key: "countingUnit", width: 30 },
  { header: "Recipe Unit", key: "recipeUnit", width: 30 },
  { header: "Par Level", key: "parLevel", width: 30 },
  { header: "Track Expiry", key: "trackExpiry", width: 30 },
  { header: "Stockable", key: "stockable", width: 30 },
  { header: "Prepare Quantity", key: "prepareQuantity", width: 30 },
  { header: "Unit Cost", key: "unitCost", width: 30 },
  { header: "HSN Code", key: "hsnCode", width: 30 },
  { header: "Ledger", key: "ledger", width: 30 },
  { header: "Default Package", key: "defaultPackage", width: 30 },
  { header: "Status", key: "activeStatus", width: 30 },
];

// Inventory packages sheet columns
const inventoryPackagesColumns = [
  { header: "Item Code", key: "itemCode", width: 30 },
  { header: "Item Name", key: "itemName", width: 30 },
  { header: "Package Name", key: "name", width: 30 },
  { header: "Package Code", key: "packageCode", width: 30 },
  { header: "Quantity", key: "quantity", width: 30 },
  { header: "Unit Cost", key: "unitCost", width: 30 },
  { header: "Full Weight", key: "fullWeight", width: 30 },
  { header: "Empty Weight", key: "emptyWeight", width: 30 },
];

// Inventory ingredients sheet columns
const inventoryIngredientsColumns = [
  { header: "Item Name", key: "itemName", width: 30 },
  { header: "Ingredient Item Code", key: "ingItemCode", width: 30 },
  { header: "Ingredient Item Name", key: "ingItemName", width: 30 },
  { header: "Quantity", key: "quantity", width: 30 },
];

// Recipe sheet columns
const recipeColumns = [
  { header: "Recipe Name", key: "name", width: 30 },
  { header: "Recipe Code", key: "recipeCode", width: 30 },
  { header: "Quantity", key: "quantity", width: 30 },
  { header: "Recipe Type", key: "recipeType", width: 30 },
  { header: "Tags", key: "tags", width: 30 },
  { header: "Recipe Unit", key: "recipeUnit", width: 30 },
  { header: "Recipe Cost", key: "cost", width: 30 },
  { header: "Status", key: "activeStatus", width: 30 },
];

// Recipe ingredients sheet columns
const recipeIngredientsColumns = [
  { header: "Recipe Name", key: "recipeName", width: 30 },
  { header: "Ingredient Item Code", key: "itemCode", width: 30 },
  { header: "Ingredient Item Name", key: "itemName", width: 30 },
  { header: "Recipe Unit", key: "recipeUnit", width: 30 },
  { header: "Quantity", key: "quantity", width: 30 },
];

module.exports = {
  vendorColumns,
  categoryColumns,
  subCategoryColumns,
  houseUnitColumns,
  inventoryItemsColumns,
  inventoryPackagesColumns,
  inventoryIngredientsColumns,
  recipeColumns,
  recipeIngredientsColumns,
  tagColumns,
  taxColumns,
};
