const { REPORTS, REPORT_INFORMATION } = require("@/defs/reportDefs");
const { validateAndPrepareFilters, constructColumns } = require("@/helpers/reportHelper");
const { ResultType } = require("@/helpers/render");
const { createXlsxReport } = require("@/helpers/xlsxReportUtility");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");

const { fetchTransferReportData } = require("@/repositories/stockMovementReportRepo");

/**
 * NewProcurementReport
 * --------------------
 * Identifies collection, determines aggregateType,
 * fetches snapshot immediately on construction,
 * and exposes result scaffolding + context.
 */
class NewProcurementReport {
  #headers = [];
  constructor(tenantId, payload, reportType) {
    if (!tenantId) throw new Error("Tenant ID is required");

    const reportInfo = REPORT_INFORMATION[reportType];
    if (!reportInfo) throw new Error("Invalid report type");

    this.#headers = reportInfo.headers;

    const { filters = {}, columns = [] } = payload || {};

    // Expose everything upfront
    this._options = {
      _filters: validateAndPrepareFilters(tenantId, filters),
      columns,
      reportType,
      aggregateType: reportInfo.aggregateType,
    };

    // Create default result skeleton
    this.result = {
      id: reportInfo.id,
      name: reportInfo.name,
      headers: this.#headers,
      tenantId,
      data: [],
      totalRow: {},
      payload,
      _meta: null
    };

    // fetch immediately (sync-like)
    // store the promise — controller will await it
    const { fetchFn } = this.#identify(this._options._filters, reportInfo.id);
    this._snapPromise = fetchFn(tenantId, this._options._filters);
  }

  /**
   * Resolve Firestore snapshot
   */
  async snap() {
    this._snap = await this._snapPromise;
    return this._snap;
  }

  generate(resultType) {
    this.result.headers = constructColumns(
      this.#headers,
      this._options.columns,
      this.result._meta
    )
    return this.#output(resultType);
  }

  #output(resultType) {
    switch (resultType) {
      case ResultType.EXCEL:
        return createXlsxReport(this.result)
      default:
        return this.result
    }
  }

  /**
   * Identify collection and fetch function.
   * @private
   */
  #identify(filters, reportType) {
    return { fetchFn: fetchTransferReportData };
  }
}

/**
 * Transfer List Report
 * ---------------------
 */
const getTransferListReport = async (tenantId, payload, resultType) => {
  const report = new NewProcurementReport(tenantId, payload, REPORTS.TRANSFER_LIST);
  const snapshot = await report.snap();

  report.result.totalRow = { totalValue: 0 };

  snapshot.forEach((doc) => {
    const data = doc.data();

    const result = {
      transferNo: data.transferNumber,
      from: data.issuer?.name,
      to: data.requester?.name,
      createdAt: FD.toFormattedDate(data.requestedBy?.time),
      createdBy: data.requestedBy?.name,
      dispatchStatus: data.dispatchStatus,
      receiveStatus: data.receiveStatus
    };

    report.result.data.push(result);
    report.result.totalRow.totalValue += data.totalValue;
  });

  return report.generate(resultType);
};

/**
 * Dispatch Transfer Report
 * ---------------------
 */
const getDispatchTransferReport = async (tenantId, payload, resultType) => {
  const report = new NewProcurementReport(tenantId, payload, REPORTS.DISPATCH_TRANSFER);
  const snapshot = await report.snap();

  report.result.totalRow = { totalValue: 0 };

  snapshot.forEach((doc) => {
    const data = doc.data();

    data.timeLine.forEach((timeline) => {
      const result = {
        transferNo: data.transferNumber,
        dispatchNo: timeline.dispatchNo,
        dispatchedBy: timeline.dispatchedBy?.name,
        dispatchedAt: FD.toFormattedDate(timeline.dispatchedBy?.time),
        status: timeline.status,
      };
    report.result.data.push(result);
    });
    report.result.totalRow.totalValue += data.totalValue || 0;
  });
  return report.generate(resultType);
};


/**
 * Detailed Transfer Report
 * ---------------------
 */
const getDetailedTransferReport = async (tenantId, payload, resultType) => {
  const report = new NewProcurementReport(tenantId, payload, REPORTS.DETAILED_TRANSFER);
  const snapshot = await report.snap();

  report.result.totalRow = { totalValue: 0 };

  snapshot.forEach((doc) => {
    const data = doc.data();
    data.items.forEach((item) => {
      const result = {
        transferNo: data.transferNumber,
        from: data.issuer?.name,
        to: data.requester?.name,
        createdAt: FD.toFormattedDate(data.requestedBy?.time),
        createdBy: data.requestedBy?.name,
        itemName: item.itemName,
        itemCode: item.itemCode,
        unitCost: item.unitCost,
        pkg: item?.pkg.id === 'default' ? item.countingUOM : item?.pkg.name,
        requestedQuantity: item.requestedQuantity,
        dispatchedQuantity: item.dispatchedQuantity || 0,
        receivedQuantity: item.receivedQuantity || 0,
        shortageQuantity: item.shortageQuantity || 0,
      };
    report.result.data.push(result);
    });
    report.result.totalRow.totalValue += data.totalValue;
  });
  return report.generate(resultType);
};

module.exports = {
  getTransferListReport,
  getDispatchTransferReport,
  getDetailedTransferReport
};