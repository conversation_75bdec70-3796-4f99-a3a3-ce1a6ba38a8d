const axios = require("axios");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");
const schema = require("@/models/tenantSchema");
const {
  handleValidation,
  trimName,
  nameValidation,
} = require("@/utils/validation");
const {
  saveTenant,
  updateTenantById,
  updateTenantSettingsById,
  updateTenantStatusById,
  getTenantById,
  getAllTenants,
  createUserForTenant,
  checkDuplicateUserEmail,
  getApprovalNeededStatus,
  freezeMonthByTenantId,
  TENANT_COLLECTION,
} = require("@/repositories/tenantRepo");

// ================== CREATE ==================
const createTenant = async (payload) => {
  const validatedData = handleValidation(payload, schema);
  if (!validatedData) return;

  const { valid, normalizedName, error } = await nameValidation(
    validatedData.name,
    TENANT_COLLECTION
  );
  if (!valid) throw new Error(error);

  const cleanName = trimName(validatedData.name);
  const tenantData = {
    ...validatedData,
    name: cleanName,
    nameNormalized: normalizedName,
    createdAt: FD.now(),
  };

  const { tenantId, emailId, name } = await saveTenant(tenantData);

  const duplicate = await checkDuplicateUserEmail(tenantId, emailId);
  if (duplicate) throw new Error("Email ID Already Exists");

  const userId = await createUserForTenant(tenantId, name, emailId);

  await axios.post(`${process.env.AUTH_SERVER_URL}/sso/invite-user`, {
    email: emailId,
    clientId: "inventory",
    userId,
    tenantId,
    tenantName: name,
  });

  return { message: "Tenant and User Created Successfully", tenantId, userId };
};

// ================== UPDATE ==================
const updateTenant = async (id, payload) => {
  const { valid, normalizedName, error } = await nameValidation(
    payload.name,
    TENANT_COLLECTION,
    id
  );
  if (!valid) throw new Error(error);

  const cleanName = trimName(payload.name);
  return await updateTenantById(id, {
    ...payload,
    name: cleanName,
    nameNormalized: normalizedName,
  });
};

const updateTenantSettings = async (tenantId, settings) => {
  const allowedKeys = [
    "prApproval",
    "poApproval",
    "autoReceive",
    "monthEndClosing",
    "currentMonth",
  ];
  const invalidKeys = Object.keys(settings).filter(
    (k) => !allowedKeys.includes(k)
  );
  if (invalidKeys.length) throw new Error("Invalid approval setting keys");

  return await updateTenantSettingsById(tenantId, settings);
};

const freezeMonth = async (tenantId, monthName) => {
  return await freezeMonthByTenantId(tenantId, monthName);
};

const updateTenantStatus = async (id, isActive) => {
  return await updateTenantStatusById(id, isActive);
};

// ================== READ ==================
const getTenant = async (tenantId) => {
  return await getTenantById(tenantId);
};

const getTenants = async () => {
  return await getAllTenants();
};

// ================== INTERNAL UTILITY ==================
const isApprovalNeeded = async (tenantId, key) => {
  const settings = await getApprovalNeededStatus(tenantId);
  return settings[key];
};

module.exports = {
  createTenant,
  updateTenant,
  updateTenantSettings,
  updateTenantStatus,
  getTenant,
  getTenants,
  isApprovalNeeded,
  freezeMonth,
};
