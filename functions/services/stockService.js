// services/stockService.js
const StockLedgerRepo = require("@/repositories/stockLedgerRepo");
const StockRepo = require("@/repositories/stockRepo");
const { StockTransactionType } = require("@/defs/ledgerDefs");
const { paiseToRupee } = require("@/utils/money");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");
const admin = require("firebase-admin");
const db = admin.firestore();

/**
 * Generic Credit function
 * @param {Object} payload {itemCode, inventoryLocationId, qty, unitCost, uom, ledgerType, referenceId}
 */
exports.creditStock = async function (payload, t) {
  // 1. Insert credit entry in ledger
  await StockLedgerRepo.addEntry(
    {
      ...payload,
      transactionType: StockTransactionType.IN,
      remainingQty: payload.qty,
    },
    t
  );

  // 2. Update Stock table
  await StockRepo.increaseStock(payload, t);
};

exports.debitStock = async function (payload) {
  return db.runTransaction(async (t) => {
    let qtyToDeduct = payload.qty;
    const ledgerBatches = await StockLedgerRepo.getAvailableBatches(
      payload.itemId,
      payload.inventoryLocationId,
      null
    );

    for (const batch of ledgerBatches) {
      if (qtyToDeduct <= 0) break;

      const deductQty = Math.min(batch.remainingQty, qtyToDeduct);

      // 1. Update batch remainingQty
      await StockLedgerRepo.updateRemaining(
        batch.id,
        batch.remainingQty - deductQty,
        t
      );

      // 2. Insert debit entry linked to original GRN/Batch
      await StockLedgerRepo.addEntry(
        {
          ...payload,
          transactionType: StockTransactionType.OUT,
          qty: deductQty,
          unitCost: batch.unitCost,
          ledgerRefId: batch.id,
        },
        t
      );

      qtyToDeduct -= deductQty;
    }

    // 3. Update Stock table balance
    await StockRepo.decreaseStock(
      {
        itemId: payload.itemId,
        inventoryLocationId: payload.inventoryLocationId,
        packageId: payload.pkg?.id,
        qty: payload.qty,
      },
      t
    );
  });
};

/**
 * Aggregate stocks
 * @param {string} tenantId
 * @param {Object} filters
 * @param {string} aggregatorType - "item" | "item_loc" | "item_inv_loc"
 */
exports.aggregateStocks = async function (
  tenantId,
  filters,
  aggregatorType = ""
) {
  const stocks = await StockRepo.getStocks(tenantId, filters);
  if (stocks.length === 0) return [];

  const aggregated = {};
  const getGroupKey = (stock) => {
    let key;

    switch (aggregatorType) {
      case "item_loc":
        key = `${stock.itemId}_${stock.locationId}`;
        break;
      case "item_inv_loc":
        key = `${stock.itemId}_${stock.locationId}_${stock.inventoryLocationId}`;
        break;
      case "item":
        key = stock.itemId;
        break;
      default:
        key = `${stock.itemId}_${stock.inventoryLocationId}_${stock.pkg.id}`;
        break;
    }

    return key;
  };

  for (const stock of stocks) {
    const qtyCounting = stock.qty * (stock.conversionFactor || 1);

    const key = getGroupKey(stock);

    if (!aggregated[key]) {
      aggregated[key] = {
        itemId: stock.itemId,
        itemName: stock.itemName,
        itemCode: stock.itemCode,
        inventoryLocationName: stock.inventoryLocationName || null,
        locationName: stock.locationName || null,
        qty: 0,
        uom: stock.countingUOM || stock.uom,
        totalCost: 0,
        avgCost: 0,
      };
    }

    aggregated[key].qty += qtyCounting;
    aggregated[key].totalCost += stock.totalValue;
  }

  return Object.values(aggregated).map((row) => ({
    ...row,
    avgCost: paiseToRupee(row.qty > 0 ? row.totalCost / row.qty : 0),
    totalCost: paiseToRupee(row.totalCost),
  }));
};

exports.getLedgers = async function (tenantId, filters) {
  const stockLedgers = await StockLedgerRepo.getStockLedgers(tenantId, filters);
  if (stockLedgers.length === 0) return [];

  const results = [];

  for (const stock of stockLedgers) {
    results.push({
      itemId: stock.itemId,
      itemName: stock.itemName,
      itemCode: stock.itemCode,
      inventoryLocationName: stock.inventoryLocationName || null,
      locationName: stock.locationName || null,
      qty: stock.qty,
      uom: stock.countingUOM || stock.uom,
      totalCost: paiseToRupee(stock.totalCost),
      avgCost: paiseToRupee(0),
      type: stock.ledgerType,
      createdAt: FD.toFormattedDate(stock.createdAt),
      remarks: stock.remarks,
    });
  }

  return results;
};
