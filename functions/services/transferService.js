// services/transferService.js
const admin = require("firebase-admin");
const db = admin.firestore();

const { LedgerTypes } = require("@/defs/ledgerDefs");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");
const { handleValidation } = require("@/utils/validation");

const schema = require("@/schema/transferSchema");
const { error } = require("@/schema/transferSchema");
const { getNextTransferId, getNextDispatchId } = require("./counterService");
const { creditStock, debitStock } = require("./stockService");

const {
  getTransfersRepo,
  createTransfer,
  getTransferById,
  updateTransfer,
  getTransfersByLocation,
  createShortageRecord,
} = require("@/repositories/transferRepo");

const { transferStatus } = require("@/defs/transferStatusDefs");

/**
 * Create transfer
 */

const createTransferRequest = async (data,split=true) => {
  try {
    const tenantConfiguration = split; // TODO: handle based on requirement
    const validatedData = handleValidation(data, schema);
    if (!validatedData) return; // Schema validation failed

    if (!tenantConfiguration) {
      return await createSingleTransfer(validatedData, validatedData.items);
    }

    const stockableItems = validatedData.items.filter((item) => item.stockable);
    const nonStockableItems = validatedData.items.filter((item) => !item.stockable);

    const transferPromises = [
      stockableItems.length && createSingleTransfer(validatedData, stockableItems),
      nonStockableItems.length && createSingleTransfer(validatedData, nonStockableItems),
    ].filter(Boolean);

    const transfers = await Promise.all(transferPromises);
    return transfers.length === 1 ? transfers[0] : transfers;

  } catch (error) {
    throw new Error(`Failed to create transfer request: ${error.message}`);
  }
};

const createSingleTransfer = async (validatedData, items) => {
  const transferNumber = await getNextTransferId(validatedData.tenantId);
  return createTransfer({
    ...validatedData,
    items,
    transferNumber,
    dispatchStatus: transferStatus.PENDING,
    receiveStatus: transferStatus.PENDING,
  });
};



/**
 * Create dispatch
 */

const createDispatchNumber = async (data, id, userId, userName) => {
  try {
    const validatedData = handleValidation(data, schema);
    if (!validatedData) return;

    const dispatchNo = await getNextDispatchId(validatedData.tenantId);

    const entry = {
      dispatchNo,
      status: transferStatus.PENDING,
      dispatchedBy: {
        id: userId,
        name: userName,
        time: FD.now(),
      },
      items: validatedData.items.map((item) => ({
        itemId: item.itemId,
        pkg: item.pkg,
        dispatchedQuantity: item.dispatchedQuantity,
        receivedQuantity: 0,
        shortageQuantity: 0,
      })),
    };

    const updatedTimeLine = [entry, ...(validatedData.timeLine || [])];

    const requestData = {
      ...validatedData,
      timeLine: updatedTimeLine,
    };

    return await dispatchTransfer(requestData, id);
  } catch (error) {
    console.log(error, "error");
    throw Error(error.message);
  }
};

/**
 * Get transfers for location
 */
const getTransfers = async (locationId) => {
  return await getTransfersByLocation(locationId);
};

/**
 * Get transfer by ID
 */
const getTransfer = async (id, type) => {
  const transfer = await getTransferById(id, type);
  if (!transfer) throw error("Transfer not found");
  return transfer;
};

/**
 * Dispatch transfer
 */
const dispatchTransfer = async (data, id) => {
  const dispatchInfo = {
    tenantId: data.tenantId,
    locationId: data.issuer.locationId,
    locationName: data.issuer.locationName,
    inventoryLocationId: data.issuer.id,
    inventoryLocationName: data.issuer.name,
  };

  for (const item of data.items) {
    const timelineEntries = data.timeLine.filter((s) =>
      s.items.some((t) => t.itemId === item.itemId && t.pkg.id === item.pkg.id)
    );

    item.dispatchedQuantity = timelineEntries.reduce((sum, s) => {
      const Item = s.items.find(
        (i) => i.itemId === item.itemId && i.pkg.id === item.pkg.id
      );
      return sum + Item.dispatchedQuantity;
    }, 0);

    const recentEntry = data.timeLine[0];
    const dispatchedQty = recentEntry?.items.find(
      (i) => i.itemId === item.itemId && i.pkg.id === item.pkg.id
    )?.dispatchedQuantity;

    const uom =
      item.pkg && item.pkg.id !== "default" ? item.pkg.name : item.countingUOM;

    if (dispatchedQty <= 0) continue;
    await debitStock({
      ledgerType: LedgerTypes.TRANSFER_OUT,
      ...dispatchInfo,
      itemId: item.itemId,
      itemCode: item.itemCode,
      itemName: item.itemName,
      qty: dispatchedQty,
      countingUOM: uom,
      unitCost: item.unitCost || 0,
      totalCost: (item.unitCost || 0) * dispatchedQty,
      expiryDate: item.expiryDate || new Date(), // @validate date
      grnMeta: null,
      pkg: item.pkg,
      remarks: item.remarks || null,
    });
  }

  const allDispatched = data.items.every(
    (item) => item.requestedQuantity - item.dispatchedQuantity <= 0
  );
  data.dispatchStatus = allDispatched
    ? transferStatus.COMPLETED
    : transferStatus.PARTIAL;

  await updateTransfer(id, { ...data, items: data.items });

  return { ...data, id };
};

/**
 * Receive transfer
 */
const receiveTransfer = async (data, id, dispatchId) => {
  try {
    const validatedData = handleValidation(data, schema);
    if (!validatedData) return;

    return await receive(validatedData, id, dispatchId);
  } catch (error) {
    console.log(error, "error");
    throw Error(error.message);
  }
};

const receive = async (data, id, dispatchId) => {
  const transaction = await db.runTransaction(async (trans) => {
    data.timeLine = data.timeLine.map((entry) => {
      if (entry.dispatchNo === dispatchId) {
        return {
          ...entry,
          status: transferStatus.COMPLETED,
        };
      }
      return entry;
    });

    data.receiveStatus =
      data.dispatchStatus === transferStatus.COMPLETED &&
      data.timeLine.every((entry) => entry.status === transferStatus.COMPLETED)
        ? transferStatus.COMPLETED
        : transferStatus.PARTIAL;

    const receiveInfo = {
      tenantId: data.tenantId,
      locationId: data.requester.locationId,
      locationName: data.requester.locationName,
      inventoryLocationId: data.requester.id,
      inventoryLocationName: data.requester.name,
    };

    for (const item of data.items) {
      const timelineEntries = data.timeLine.filter((s) =>
        s.items.some(
          (t) => t.itemId === item.itemId && t.pkg.id === item.pkg.id
        )
      );

      item.receivedQuantity = timelineEntries.reduce((sum, s) => {
        const Item = s.items.find(
          (i) => i.itemId === item.itemId && i.pkg.id === item.pkg.id
        );
        return sum + Item.receivedQuantity;
      }, 0);

      item.shortageQuantity = Math.max(
        item.dispatchedQuantity - item.receivedQuantity,
        0
      );

      const recentEntry = data.timeLine[0];
      const receivedQty = recentEntry?.items.find(
        (i) => i.itemId === item.itemId && i.pkg.id === item.pkg.id
      )?.receivedQuantity;

      const uom =
        item.pkg && item.pkg.id !== "default"
          ? item.pkg.name
          : item.countingUOM;

      await creditStock(
        {
          ledgerType: LedgerTypes.TRANSFER_IN,
          ...receiveInfo,
          itemId: item.itemId,
          itemCode: item.itemCode,
          itemName: item.itemName,
          qty: receivedQty,
          countingUOM: uom,
          unitCost: item.unitCost || 0,
          totalCost: (item.unitCost || 0) * receivedQty,
          expiryDate: item.expiryDate || new Date(), // @validate date
          grnMeta: null,
          categoryId: item.categoryId,
          subcategoryId: item.subcategoryId,
          categoryName: item.categoryName,
          subcategoryName: item.subcategoryName,
          pkg: item.pkg,
          remarks: item.remarks || null,
        },
        trans
      );
    }

    await handleShortages(data);

    await updateTransfer(id, { ...data, items: data.items }, trans);
  });

  await transaction;
};

/**
 * Create shortage record
 */
async function handleShortages(data) {
  const shortageRecords = [];

  for (const timeline of data.timeLine) {
    const shortageItems = timeline.items.filter((i) => i.shortageQuantity > 0);
    if (!shortageItems.length) continue;

    const record = {
      tenantId: data.tenantId,
      dispatchNo: timeline.dispatchNo,
      locationId: data.issuer.id,
      inventoryLocationId: data.issuer.locationId,
      createdBy: timeline.receivedBy,
      items: shortageItems.map((t) => {
        const match = data.items.find((d) => d.itemId === t.itemId);
        return {
          itemName: match?.itemName,
          itemId: t.itemId,
          quantity: t.shortageQuantity,
          reason: t.reason,
          type: "reduction",
        };
      }),
    };

    shortageRecords.push(record);
  }

  if (shortageRecords.length) {
    await createShortageRecord(shortageRecords);
  }
}

/**
 * Close transfer
 */
const closeTransfer = async (data, id) => {
  try {
    await db.runTransaction(async (trans) => {
      const updatedData = {
        ...data,
        dispatchStatus: transferStatus.COMPLETED,
        receiveStatus: transferStatus.COMPLETED,
      };
      await updateTransfer(id, updatedData, trans);
    });
  } catch (error) {
    throw Error(error.message);
  }
};

const aggregateTransfers = async (tenantId, filters) => {
  const transfers = await getTransfersRepo(tenantId, filters);
  const result = transfers.map((doc) => ({
    transferNumber: doc.transferNumber,
    id: doc.id,
    requester: doc.requester.name,
    issuer: doc.issuer.name,
    requestedDate: FD.toFormattedDate(doc.requestedBy.time),
    requestedBy: doc.requestedBy.name,
    dispatchStatus: doc.dispatchStatus,
    receiveStatus: doc.receiveStatus,
    timeLine: doc.timeLine ? doc.timeLine : [],
  }));

  result.sort((a, b) => b.transferNumber.localeCompare(a.transferNumber));

  return result;
};

module.exports = {
  createTransferRequest,
  createDispatchNumber,
  getTransfers,
  getTransfer,
  dispatchTransfer,
  receiveTransfer,
  closeTransfer,
  aggregateTransfers,
};
