// services/purchaseService.js
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");
const { getTransferDataById } = require("@/repositories/transferRepo");
const { getVendorById } = require("@/repositories/vendorRepo");
const { getLocationById } = require("@/repositories/locationRepo");
const { getGrnByID } = require("@/services/grnService.js");
const {
  getPRById,
  getPurchaseOrderById,
} = require("@/services/purchaseService.js");

const {
  formatNumber,
  generateAmountInWordsRow,
} = require("@/helpers/printHelper");

async function preparePOData(id, tenantId) {
  const po = await getPurchaseOrderById(id);
  if (!po) return null;

  const vendorId = po.vendor?.id;
  if (!vendorId) return null;

  const vendor = await getVendorById(tenantId, vendorId);
  if (!vendor) return null;

  const vendorObj = {
    type: "Vendor",
    Name: vendor.name,
    Contact: vendor.contactName,
    Email: vendor.contactEmailId,
    PAN: vendor.panNo,
    GST: vendor.gstNo,
    Address: vendor?.address?.address
      ? [
          vendor.address.address,
          [vendor.address.city, vendor.address.state, vendor.address.pincode]
            .filter(Boolean)
            .join(", "),
        ]
          .filter(Boolean)
          .join("\n")
      : "",
  };

  const poObj = {
    type: "PO",
    "PO No.": po.poNumber,
    Date: po.requestedTime,
    GST: vendor.gstNo,
    PAN: vendor.panNo,
    Creator: po.requestedBy?.name,
    Workarea: po.location?.name,
    Delivery: po.deliveryDate,
    "PR No.": po.prNumber,
  };

  const poTerms = vendor.poTerms?.split(",").filter(Boolean) || [];
  const paymentTerms = vendor.paymentTerms?.split(",").filter(Boolean) || [];
  const remarks = vendor.remarks?.split(",").filter(Boolean) || [];
  const itemsTable = await preparePurchaseTable(po);

  const grandTotal =
    po.items.reduce((sum, item) => sum + item.totalPrice, 0) + 50;

  return {
    po,
    vendor,
    vendorObj,
    poObj,
    poTerms,
    paymentTerms,
    remarks,
    grandTotal,
    itemsTable,
  };
}

async function prepareTransferData(id, type, dispatchNo) {
  const transfer = await getTransferDataById(id);
  if (!transfer) return null;

  const transferObj = {
    type: type,
    "Transfer No.": transfer.transferNumber,
    Requester: transfer.requester.name,
    Issuer: transfer.issuer.name,
    "Requested By": transfer.requestedBy.name,
    "Requested Date": FD.toFormattedDate(transfer.requestedBy.time),
  };

  if (type === "Receive") {
    const dispatchEntry = transfer.timeLine.find(
      (entry) => entry.dispatchNo === dispatchNo
    );

    if (dispatchEntry) {
      const dispatchBy = dispatchEntry.dispatchedBy.name;
      const dispatchAt = dispatchEntry.dispatchedBy.time;

      transferObj["Dispatched By"] = dispatchBy;
      // transferObj["Dispatched Date"] = FD.toFormattedDate(dispatchAt);
    }
  }

  return { transfer, transferObj };
}

async function prepareGRNData(grnId, tenantId) {
  const grn = await getGrnByID(grnId, tenantId);
  if (!grn) return null;

  const vendorId = grn.vendorId;
  if (!vendorId) return null;

  const vendor = await getVendorById(tenantId, vendorId);
  if (!vendor) return null;

  const vendorObj = {
    type: "Vendor",
    Name: vendor.name,
    Contact: vendor.contactName,
    Email: vendor.contactEmailId,
    PAN: vendor.panNo,
    GST: vendor.gstNo,
    Address: vendor?.address?.address
      ? [
          vendor.address.address,
          [vendor.address.city, vendor.address.state, vendor.address.pincode]
            .filter(Boolean)
            .join(", "),
        ]
          .filter(Boolean)
          .join("\n")
      : "",
  };

  const grnObj = {
    type: "GRN",
    "GRN No.": grn.grnNumber,
    Date: grn.createdAt,
    GST: vendor.gstNo,
    PAN: vendor.panNo,
    Creator: grn.createdBy.name ? grn.createdBy.name : "",
    Workarea: grn.inventoryLocation.name ? grn.inventoryLocation.name : "",
    "PO No.": grn.poNumber,
  };

  const grnTerms = vendor.grnTerms?.split(",").filter(Boolean) || [];
  const paymentTerms = vendor.paymentTerms?.split(",").filter(Boolean) || [];
  const remarks = vendor.remarks?.split(",").filter(Boolean) || [];
  const grandTotal =
    grn.items.reduce((sum, item) => sum + item.totalValue, 0) + 0;
  const itemsTable = await preparePurchaseTable(grn);
  return {
    grn,
    vendor,
    vendorObj,
    grnObj,
    grnTerms,
    paymentTerms,
    remarks,
    grandTotal,
    itemsTable,
  };
}

async function preparePRData(prId, tenantId) {
  const pr = await getPRById(prId);
  if (!pr) return null;
  const firstCol = {
    type: "PR",
    "PR No.": pr.prNumber,
    "Delivery Date": pr.deliveryDate,
  };
  const secondCol = {
    "Created By": pr.requestedBy.name ? pr.requestedBy.name : "",
    "Created At": pr.statusTimeline[0].time,
  };
  const itemsTable = await preparePurchaseTable(pr,'PR');
  return {
    pr,
    firstCol,
    secondCol,
    itemsTable,
  };
}


async function preparePurchaseTable(data, type = 'GRN') {
  const items = data['items'];
  const qtyKey = type === 'PR' ? 'quantity' : 'receivedQty';

  return {
    items: items,
    columns: [
      { key: 'index', header: '#', width: 20 },
      {
        key: 'itemName',
        header: 'Item Name',
        width: '*',
        getValue: (item) => item.foc ? `${item.itemName} (FOC)` : item.itemName,
      },
      { key: 'pkg', header: 'Pkg', width: 40, getValue: (item) => item.pkg?.name || '' },
      { key: qtyKey, header: 'Qty', width: 40, format: formatNumber },
      { key: 'unitCost', header: 'Unit Cost', width: 40, format: formatNumber },
      { key: 'totalDiscount', header: 'Disc', width: 25, format: (v) => formatNumber(v || 0) },
      { key: 'taxRate', header: 'Tax', width: 25, format: formatNumber },
      { key: 'totalCess', header: 'Cess', width: 28, format: formatNumber },
      { key: 'netAmount', header: 'Net Amt', width: 40, format: formatNumber },
      { key: 'totalTaxAmount', header: 'Tax Amt', width: 40, format: formatNumber },
      { key: 'totalAmount', header: 'Total', width: 40, format: formatNumber },
    ],

    totalRows: [
      {
        label: 'Total',
        values: {
          totalCess: data.totalCess,
          netAmount: data.netAmount,
          totalTaxAmount: data.totalTaxAmount,
          totalAmount: data.netAmount + data.totalTaxAmount,
        },
      },
      // Only show FOC if type is NOT 'PR'
      ...(type !== 'PR'
        ? [
            {
              label: 'FOC',
              values: {
                totalAmount: data.totalFocAmount ? data.totalFocAmount : 0,
              },
            },
          ]
        : []),
      ...(data['charges'] && data.charges.length > 0 ? transformArray(data.charges) : []),
      {
        label: 'Grand Total',
        values: {
          totalAmount: data.totalAmount ? data.totalAmount : 0,
        },
      },
    ],

    specialRows: [
      (columnCount) => generateAmountInWordsRow(data.totalAmount, columnCount),
    ],
  };
}


function transformArray(data) {
  return data.map(item => ({
    label: item.name,
    values: {
      totalAmount: item.valueAmt
    }
  }));
}


module.exports = {
  preparePOData,
  prepareTransferData,
  getLocationById,
  prepareGRNData,
  preparePRData,
};
