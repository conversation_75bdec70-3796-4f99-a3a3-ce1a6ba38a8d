// services/stockTransactionService.js
const admin = require("firebase-admin");
const db = admin.firestore();
const grnSchema = require("@/schema/grnSchema");

const { LedgerTypes } = require("@/defs/ledgerDefs");
const { getNextGrnId } = require("./counterService");
const { creditStock } = require("./stockService");
const StockLedgerRepo = require("@/repositories/stockLedgerRepo");
const { StockTransactionType } = require("@/defs/ledgerDefs");
const StockRepo = require("@/repositories/stockRepo");

const {
  TIME_OPTION,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");
const { rupeeToPaise } = require("@/utils/money");
const { handleValidation } = require("@/utils/validation");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const { purchaseStatus } = require("@/defs/purchaseStatusDefs");

/**
 * =======================
 * IN Operations (Credit)
 * =======================
 */

/**
 * Create a new GRN (Goods Received Note)
 * - Validates items against PO
 * - Updates PO items receivedQty & notes
 * - Persists GRN in Firestore
 * - Calls creditStock for each item to update stock & ledger
 * - Returns the full GRN document
 * - Stores only summary information in GRN (items removed since ledger tracks details)
 */
async function createGRN(
  {
    poData,
    grnDate,
    invoiceDate,
    invoiceNumber,
    grnItems,
    receivedById,
    receivedByName,
    paymentTerms,
    poTerms,
    remarks,
    totalAmount: grnTotalValue,
    totalCess,
    totalChargeAmount,
    totalDiscount,
    totalFocAmount,
    totalTaxAmount,
    netAmount,
    grossAmount,
    taxes,
    charges,
  },
  transaction
) {
  // Validate GRN items against PO and update PO items
  const validItems = grnItems.map((grnItem) => {
    const poItem = poData.items.find((i) => {
      if (i.pkg?.id) {
        return i.itemId === grnItem.itemId && i.pkg.id === grnItem.pkg.id;
      }
      return i.itemId === grnItem.itemId;
    });
    if (!poItem) {
      throw new Error(`Item ${grnItem.itemId} not found in PO`);
    }
    poItem.receivedQty = (poItem.receivedQty || 0) + grnItem.receivedQty;

    return {
      itemId: poItem.itemId,
      itemCode: poItem.itemCode,
      itemName: poItem.itemName,
      qtyOrdered: poItem.quantity,
      qtyReceived: grnItem.receivedQty,
      unitCost: rupeeToPaise(grnItem.unitCost),
      purchaseUOM: grnItem.purchaseUOM,
      expiryDate: grnItem.expiryDate || new Date(), // @validate date
      remarks: grnItem.remarks || "",
      categoryId: poItem.categoryId,
      subcategoryId: poItem.subcategoryId,
      categoryName: poItem.categoryName,
      subcategoryName: poItem.subcategoryName,
      totalAmount: rupeeToPaise(grnItem.totalAmount),
      totalCess: rupeeToPaise(grnItem.totalCess),
      totalChargeAmount: rupeeToPaise(grnItem.totalChargeAmount),
      totalDiscount: rupeeToPaise(grnItem.totalDiscount),
      totalFocAmount: rupeeToPaise(grnItem.totalFocAmount),
      totalTaxAmount: rupeeToPaise(grnItem.totalTaxAmount),
      netAmount: rupeeToPaise(grnItem.netAmount),
      grossAmount: rupeeToPaise(grnItem.grossAmount),
      taxes: grnItem.taxes,
      charges: grnItem.charges,
      taxRate: grnItem.taxRate,
      pkg: grnItem.pkg,
      remarks: poItem.remarks || "",
      hsnCode: poItem.hsnCode,
      foc: grnItem.foc,
    };
  });

  // Generate GRN ID using counter service
  const grnNumber = await getNextGrnId(poData.tenantId);

  // Form GRN summary (items removed, ledger contains detailed item movements)
  const grnRef = db.collection(COLLECTIONS.GRN).doc(); // @todo: move to repo

  const data = {
    tenantId: poData.tenantId,
    grnNumber,
    grnDate: FD.toFirestore(grnDate, TIME_OPTION.START),
    invoiceDate: FD.toFirestore(invoiceDate, TIME_OPTION.START),
    invoiceNumber,
    id: grnRef.id,
    poId: poData.poId,
    poNumber: poData.poNumber,
    vendorId: poData.vendor.id,
    vendorName: poData.vendor.name,
    vendor: { ...poData.vendor, paymentTerms, poTerms },
    location: {
      id: poData.location.id,
      name: poData.location.name,
    },
    inventoryLocation: {
      id: poData.inventoryLocation.id,
      name: poData.inventoryLocation.name,
    },
    remarks: remarks || null,
    createdBy: {
      id: receivedById,
      name: receivedByName,
    },
    createdAt: FD.now(),
    items: validItems,
    status: purchaseStatus.COMPLETED,
    statusTimeline: [
      {
        name: purchaseStatus.COMPLETED,
        time: FD.now(),
        by: {
          name: receivedByName,
          id: receivedById,
        },
      },
    ],
    totalAmount: rupeeToPaise(grnTotalValue),
    totalChargeAmount: rupeeToPaise(totalChargeAmount),
    netAmount: rupeeToPaise(netAmount),
    grossAmount: rupeeToPaise(grossAmount),
    totalFocAmount: rupeeToPaise(totalFocAmount),
    totalTaxAmount: rupeeToPaise(totalTaxAmount),
    totalDiscount: rupeeToPaise(totalDiscount),
    totalCess: rupeeToPaise(totalCess),
    taxes,
    charges,
  };

  const grn = handleValidation(data, grnSchema);
  if (!grn) return;

  // Save GRN summary in Firestore
  await transaction.set(grnRef, grn);

  // Prepare GRN meta object once
  const grnMeta = {
    id: grnRef.id,
    grnNumber,
    poId: poData.poId,
    vendorId: poData.vendor.id,
    vendorName: poData.vendor.name,
  };

  const poInfo = {
    tenantId: poData.tenantId,
    locationId: poData.location.id,
    locationName: poData.location.name,
    inventoryLocationId: poData.inventoryLocation?.id,
    inventoryLocationName: poData.inventoryLocation?.name,
  };

  // Process each valid item and call creditStock
  for (const item of validItems) {
    let quantity = item.qtyReceived;

    const uom =
      item.pkg && item.pkg.id !== "default" ? item.pkg.name : item.purchaseUOM;

    await creditStock(
      {
        ledgerType: LedgerTypes.GRN,
        ...poInfo,
        itemId: item.itemId,
        itemCode: item.itemCode,
        itemName: item.itemName,
        categoryId: item.categoryId,
        subcategoryId: item.subcategoryId,
        categoryName: item.categoryName,
        subcategoryName: item.subcategoryName,
        orderedQty: item.qtyOrdered,
        qty: quantity,
        countingUOM: uom,
        unitCost: item.unitCost,
        totalCost: item.totalAmount,
        expiryDate: item.expiryDate,
        discount: item.totalDiscount,
        taxRate: item.taxRate,
        taxAmount: item.totalTaxAmount,
        grnMeta,
        pkg: item.pkg,
        remarks: item.remarks,
        foc: item.foc,
        cess: item.totalCess,
      },
      transaction
    );
  }

  return {
    grn,
    updatedPO: poData,
  };
}

async function deleteGRN({
  grnId,
  tenantId,
  reason,
  removedById,
  removedByName,
  ledgerType,
}) {
  return db.runTransaction(async (t) => {
    const grnRef = db.collection(COLLECTIONS.GRN).doc(grnId);
    const grnSnap = await t.get(grnRef);
    if (!grnSnap.exists) {
      throw new Error("GRN not found");
    }

    const grn = grnSnap.data();
    if (grn.status == purchaseStatus.DELETED) {
      throw new Error("GRN already deleted");
    }

    if (grn.status == purchaseStatus.RETURN_VENDOR) {
      throw new Error("GRN already returned to vendor");
    }

    // For each GRN item
    for (const item of grn.items) {
      const itemQty = item.qtyReceived;

      //1. Fetch all ledger IN entries for this GRN
      const ledgersSnap = await db
        .collection(COLLECTIONS.LEDGERS)
        .where("tenantId", "==", tenantId)
        .where("ledgerType", "==", LedgerTypes.GRN)
        .where("grnMeta.id", "==", grnId)
        .where("itemId", "==", item.itemId)
        .where("pkg.id", "==", item.pkg.id)
        .orderBy("createdAt", "desc")
        .limit(1)
        .get();

      if (ledgersSnap.empty) {
        throw new Error(`No ledger found for GRN item: ${item.itemId}`);
      }

      // 2. Update the original IN ledger to remainingQty = 0
      ledgersSnap.forEach((snap) => {
        t.update(snap.ref, { remainingQty: 0, updatedAt: FD.now() });
      });

      // 3. Create a new OUT ledger to reverse movement
      await StockLedgerRepo.addEntry(
        {
          tenantId,
          ledgerType: ledgerType,
          transactionType: StockTransactionType.OUT,
          itemId: item.itemId,
          itemCode: item.itemCode,
          itemName: item.itemName,
          categoryId: item.categoryId,
          subcategoryId: item.subcategoryId,
          categoryName: item.categoryName,
          subcategoryName: item.subcategoryName,
          qty: itemQty,
          remainingQty: 0, // OUT entries always zero
          countingUOM: item.pkg?.name || item.purchaseUOM,
          unitCost: item.unitCost,
          totalCost: item.totalAmount,
          expiryDate: item.expiryDate,
          discount: item.totalDiscount,
          taxRate: item.taxRate,
          taxAmount: item.totalTaxAmount,
          pkg: item.pkg,
          grnMeta: {
            id: grnId,
            grnNumber: grn.grnNumber,
            reversed: true,
          },
          locationId: grn.location.id,
          locationName: grn.location.name,
          inventoryLocationId: grn.inventoryLocation.id,
          inventoryLocationName: grn.inventoryLocation.name,
        },
        t
      );

      // 4. Decrease stock
      await StockRepo.decreaseStock(
        {
          itemId: item.itemId,
          inventoryLocationId: grn.inventoryLocation.id,
          packageId: item.pkg?.id,
          qty: itemQty,
        },
        t
      );
    }

    // 5. Mark GRN as deleted
    t.update(grnRef, {
      status:
        ledgerType == LedgerTypes.GRN_DELETE
          ? purchaseStatus.DELETED
          : purchaseStatus.RETURN_VENDOR,
      removedAt: FD.now(),
      updatedAt: FD.now(),
      removedBy: {
        id: removedById,
        name: removedByName,
      },
      reason: reason || null,
      statusTimeline: [
        ...grn.statusTimeline,
        {
          name:
            ledgerType == LedgerTypes.GRN_DELETE
              ? purchaseStatus.DELETED
              : purchaseStatus.RETURN_VENDOR,
          time: FD.now(),
          by: {
            name: removedByName,
            id: removedById,
          },
        },
      ],
    });

    return {
      grnId,
      message:
        ledgerType == LedgerTypes.GRN_DELETE
          ? "GRN deleted successfully"
          : "GRN returned to vendor successfully",
    };
  });
}

module.exports = {
  createGRN,
  deleteGRN,
};
