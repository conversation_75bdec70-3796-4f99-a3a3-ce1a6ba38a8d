const admin = require("firebase-admin");
const db = admin.firestore();

const schema = require("@/models/adjustmentSchema");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");
const { handleValidation } = require("@/utils/validation");
const {
  saveAdjustment,
  getAllAdjustments,
  getById,
  updateById,
} = require("@/repositories/adjustmentRepo");
const { getNextAdjustmentId } = require("./counterService");
const { getInventoryItemStocks } = require("@/repositories/stockRepo");
const { LedgerTypes } = require("@/defs/ledgerDefs");
const { creditStock, debitStock } = require("./stockService");

const createAdjustment = async (data) => {
  try {
    const validatedData = handleValidation(data, schema);
    if (!validatedData) return;

    const adjustmentNumber = await getNextAdjustmentId(data.tenantId);

    const result = await db.runTransaction(async (trans) => {
      const items = await getInventoryItemStocks(
        validatedData.workAreaId,
        validatedData.items
      );

      // CREDIT ITEMS
      for (const item of items.filter((i) => i.adjustmentType == "addition")) {
        const qty = item.adjustmentQuantity - item.inStock;
        await creditStock(
          {
            ledgerType: LedgerTypes.ADJUSTMENT_CREDIT,
            tenantId: validatedData.tenantId,
            locationId: validatedData.locationId,
            locationName: validatedData.locationName,
            inventoryLocationId: validatedData.workAreaId,
            inventoryLocationName: validatedData.workAreaName,
            itemId: item.itemId,
            itemCode: item.itemCode,
            itemName: item.itemName,
            qty,
            countingUOM: item.countingUOM,
            recipeUOM: item.recipeUOM || "",
            conversionFactor: item.conversionFactor || 1,
            unitCost: item.unitCost || 0,
            totalCost: (item.unitCost || 0) * qty,
            expiryDate: item.expiryDate || null,
            grnMeta: null,
            categoryId: item.categoryId,
            subcategoryId: item.subcategoryId,
            categoryName: item.categoryName,
            subcategoryName: item.subcategoryName,
            pkg: item.pkg,
            remarks: item.remarks || null,
          },
          trans
        );
      }

      // DEBIT ITEMS
      for (const item of items.filter((i) => i.adjustmentType == "reduction")) {
        const qty = item.inStock - item.adjustmentQuantity;
        await debitStock(
          {
            ledgerType: LedgerTypes.ADJUSTMENT_DEBIT,
            tenantId: validatedData.tenantId,
            locationId: validatedData.locationId,
            locationName: validatedData.locationName,
            inventoryLocationId: validatedData.workAreaId,
            inventoryLocationName: validatedData.workAreaName,
            itemId: item.itemId,
            itemCode: item.itemCode,
            itemName: item.itemName,
            qty,
            countingUOM: item.countingUOM,
            unitCost: item.unitCost || 0,
            totalCost: (item.unitCost || 0) * qty,
            expiryDate: item.expiryDate || null,
            grnMeta: null,
            categoryId: item.categoryId,
            subcategoryId: item.subcategoryId,
            categoryName: item.categoryName,
            subcategoryName: item.subcategoryName,
            pkg: item.pkg,
            remarks: item.remarks || null,
          },
          trans
        );
      }

      const requestData = {
        ...validatedData,
        adjustmentNumber,
      };

      const adjustmentDoc = await saveAdjustment(requestData, trans);
      return adjustmentDoc;
    });

    return result;
  } catch (error) {
    console.error("Error in createAdjustment:", error);
    throw new Error(error.message);
  }
};

const getAdjustments = async (filters) => {
  const adjustments = await getAllAdjustments(filters);

  const result = adjustments.map((s) => {
   return {
      adjustmentNumber: s.adjustmentNumber,
      locationName: s.locationName,
      workAreaName: s.workAreaName,
      id: s.id,
      adjustmentDate: FD.toFormattedDate(s.adjustmentDate),
      requestedBy: s.requestedBy.name,
      requestedAt: FD.toFormattedDate(s.requestedBy.time),
    };
  });
  return result;
};

const getAdjustmentById = async (id) => {
  try {
    const data = await getById(id);
    if (!data) return null;
    return {
      ...data,
      adjustmentDate: FD.toFormattedDate(data.adjustmentDate),
      requestedBy: {
        ...data.requestedBy,
        time: FD.toFormattedDate(data.requestedBy.time),
      },
    };
  } catch (error) {
    throw Error(err.message);
  }
};

const updateAdjustment = async (id, payload) => {
  try {
    const data = handleValidation(payload, schema);
    if (!data) return;
    const result = await updateById(id, data);
    return result;
  } catch (error) {
    throw Error(err.message);
  }
};

module.exports = {
  createAdjustment,
  getAdjustments,
  getAdjustmentById,
  updateAdjustment,
};
