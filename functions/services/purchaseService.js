// services/purchaseService.js
const admin = require("firebase-admin");
const db = admin.firestore();
const prSchema = require("@/models/purchaseRequestSchema");
const poSchema = require("@/models/purchaseOrderSchema");
const { handleValidation } = require("@/utils/validation");

const {
  getPOById,
  updatePO,
  getPurchaseOrders,
} = require("@/repositories/poRepo");
const { createGRN } = require("./stockTransactionService");
const {
  getPurchaseRequests,
  savePurchaseRequest,
  getPurchaseRequestById,
  updatePurchaseRequestById,
} = require("@/repositories/prRepo");
const { paiseToRupee, rupeeToPaise } = require("@/utils/money");
const { purchaseStatus } = require("@/defs/purchaseStatusDefs");
const {
  DATE_FORMAT,
  TIME_OPTION,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");
const {
  getNextPurchaseRequestId,
  getNextPurchaseOrderId,
} = require("@/services/counterService");
const { isApprovalNeeded } = require("@/services/tenantService");
const { savePurchaseOrder } = require("@/repositories/poRepo");
const { getVendorById } = require("@/repositories/vendorRepo");
const {
  createTransferRequest,
  createDispatchNumber,
  receiveTransfer,
} = require("@/services/transferService");
const { transferStatus } = require("@/defs/transferStatusDefs");
const cartHelper = require("@/helpers/cartHelper");

/**
 * Receive items against a PO
 * - Validates items against PO
 * - Updates PO items receivedQty & notes
 * - Persists GRN in Firestore
 * - Calls creditStock for each item to update stock & ledger
 * - Returns the full GRN document
 */
const receivePO = async ({
  poId,
  grnDate,
  invoiceDate,
  invoiceNumber,
  grnItems,
  receivedById,
  receivedByName,
  poOption,
  directIssueList,
  paymentTerms,
  poTerms,
  remarks = null,
  totalAmount,
  totalCess,
  totalChargeAmount,
  totalDiscount,
  totalFocAmount,
  totalTaxAmount,
  netAmount,
  grossAmount,
  taxes = [],
  charges = [],
}) => {
  const t = await db.runTransaction(async (trans) => trans);

  const poData = await getPOById(poId, t);
  if (!poData) throw new Error("PO not found");

  // @todo: validate po status & more..

  // Create GRN summary and update PO items
  const { grn, updatedPO } = await createGRN(
    {
      poData,
      grnDate,
      invoiceDate,
      invoiceNumber,
      grnItems,
      receivedById,
      receivedByName,
      paymentTerms,
      poTerms,
      remarks,
      totalAmount,
      totalCess,
      totalChargeAmount,
      totalDiscount,
      totalFocAmount,
      totalTaxAmount,
      netAmount,
      grossAmount,
      taxes,
      charges,
    },
    t
  );

  const updateStatusTimeline = (status) => {
    updatedPO.statusTimeline.push({
      name: status,
      time: FD.now(),
      by: {
        name: receivedByName,
        id: receivedById,
      },
    });
    return updatedPO.statusTimeline;
  };

  switch (poOption) {
    case 1:
      await updatePO(
        poId,
        {
          items: updatedPO.items,
          status: purchaseStatus.PARTIAL,
          statusTimeline: updateStatusTimeline(purchaseStatus.PARTIAL),
          vendor: { ...poData.vendor, paymentTerms, poTerms },
          remarks,
        },
        t
      );
      break;
    case 2:
      await updatePO(
        poId,
        {
          items: updatedPO.items,
          status: purchaseStatus.COMPLETED,
          statusTimeline: updateStatusTimeline(purchaseStatus.COMPLETED),
          vendor: { ...poData.vendor, paymentTerms, poTerms },
          remarks,
          invoiceDate: FD.toFirestore(invoiceDate, TIME_OPTION.START),
          grnDate: FD.toFirestore(grnDate, TIME_OPTION.START),
          invoiceNumber,
        },
        t
      );
      // createPO
      break;
    default:
      await updatePO(
        poId,
        {
          items: updatedPO.items,
          status: purchaseStatus.COMPLETED,
          statusTimeline: updateStatusTimeline(purchaseStatus.COMPLETED),
          vendor: { ...poData.vendor, paymentTerms, poTerms },
          remarks,
          invoiceDate: FD.toFirestore(invoiceDate, TIME_OPTION.START),
          grnDate: FD.toFirestore(grnDate, TIME_OPTION.START),
          invoiceNumber,
        },
        t
      );
      break;
  }

  // Save updated PO
  // @todo: change status, create new po from pending item

  t.commit();

  if (directIssueList && directIssueList.length) {
    await handleDirectIssueList({
      directIssueList,
      poData,
      receivedById,
      receivedByName,
    });
  }

  return {
    ...grn,
    totalAmount: paiseToRupee(grn.totalAmount),
  };
};

const handleDirectIssueList = async ({
  directIssueList,
  poData,
  receivedById,
  receivedByName,
}) => {
  for (const issue of directIssueList) {
    const tsNow = FD.now();

    // Step 1️: Create Transfer
    const transferPayload = {
      tenantId: poData.tenantId,
      issuer: issue.issuer,
      requester: issue.requester,
      items: issue.items.map((item) => ({
        ...item,
        requestedQuantity: item.requestedQuantity,
      })),
      requestedBy: {
        id: receivedById,
        name: receivedByName,
        time: tsNow,
      },
    };

    const createdTransfer = await createTransferRequest(transferPayload, false);

    // Step 2️: Dispatch Transfer
    const dispatchPayload = {
      ...createdTransfer,
      items: createdTransfer.items.map((item) => ({
        ...item,
        dispatchedQuantity: item.requestedQuantity,
      })),
    };

    const createdDispatch = await createDispatchNumber(
      dispatchPayload,
      createdTransfer.id,
      receivedById,
      receivedByName
    );

    // Step 3️: Receive Transfer
    const dispatchNo = createdDispatch.timeLine[0].dispatchNo;

    const receivePayload = {
      ...createdDispatch,
      items: createdDispatch.items.map((item) => ({
        ...item,
        receivedQuantity: item.dispatchedQuantity,
        shortageQuantity: 0,
      })),
      timeLine: createdDispatch.timeLine.map((entry) => ({
        ...entry,
        items: entry.items.map((i) => ({
          ...i,
          receivedQuantity: i.dispatchedQuantity,
          shortageQuantity: 0,
          reason: "",
        })),
        receivedBy: {
          id: receivedById,
          name: receivedByName,
          time: tsNow,
        },
      })),
      receiveStatus: transferStatus.COMPLETED,
      dispatchStatus: transferStatus.COMPLETED,
    };

    await receiveTransfer(receivePayload, createdTransfer.id, dispatchNo);
  }
};

const aggregatePurchaseRequests = async (tenantId, filters) => {
  const purchaseRequests = await getPurchaseRequests(tenantId, filters);

  const result = purchaseRequests.map((pr) => {
    const vendors = pr.items.map((item) => item.vendor?.id);
    return {
      id: pr.id,
      prNumber: pr.prNumber,
      deliveryDate: FD.toFormattedDate(pr.deliveryDate, DATE_FORMAT.DATE_ONLY),
      status: pr.status,
      tenantId: pr.tenantId,
      vendor: [...new Set(vendors)].length > 1 ? "-" : pr.items[0].vendor?.name,
      createdAt: FD.toFormattedDate(pr.statusTimeline[0].time),
      totalAmount: paiseToRupee(pr.totalAmount),
      requestedBy: pr.requestedBy?.name,
    };
  });
  return result;
};

const aggregatePurchaseOrders = async (tenantId, filters) => {
  const purchaseOrders = await getPurchaseOrders(tenantId, filters);

  const result = purchaseOrders.map((po) => {
    return {
      id: po.id,
      poNumber: po.poNumber,
      deliveryDate: FD.toFormattedDate(po.deliveryDate, DATE_FORMAT.DATE_ONLY),
      status: po.status,
      tenantId: po.tenantId,
      createdAt: FD.toFormattedDate(po.statusTimeline[0].time),
      totalAmount: paiseToRupee(po.totalAmount),
      vendor: po.vendor?.name,
      requestedBy: po.requestedBy?.name,
    };
  });
  return result;
};

const buildStatusTimeline = (data, isApprovalNeed) => {
  const timeline = [];
  const tsNow = FD.now();

  // Case 1: SUBMITTED
  if (data.status === purchaseStatus.SUBMITTED) {
    timeline.push({
      name: purchaseStatus.SUBMITTED,
      time: tsNow,
      by: data.requestedBy,
    });

    // Auto-approve if no approval needed
    if (!isApprovalNeed) {
      timeline.push({
        name: purchaseStatus.APPROVED,
        time: tsNow,
        by: data.requestedBy,
      });
      return {
        status: purchaseStatus.APPROVED,
        statusTimeline: timeline,
      };
    }

    return {
      status: purchaseStatus.SUBMITTED,
      statusTimeline: timeline,
    };
  }

  // Case 2: DRAFT
  if (data.status === purchaseStatus.DRAFT) {
    timeline.push({
      name: purchaseStatus.DRAFT,
      time: tsNow,
      by: data.requestedBy,
    });

    return {
      status: purchaseStatus.DRAFT,
      statusTimeline: timeline,
    };
  }

  // Case 3: COMPLETED (auto-flow full cycle)
  timeline.push(
    {
      name: purchaseStatus.DRAFT,
      time: tsNow,
      by: data.requestedBy,
    },
    {
      name: purchaseStatus.SUBMITTED,
      time: tsNow,
      by: data.requestedBy,
    },
    {
      name: purchaseStatus.APPROVED,
      time: tsNow,
      by: data.requestedBy,
    },
    {
      name: purchaseStatus.COMPLETED,
      time: tsNow,
      by: data.requestedBy,
    }
  );

  return {
    status: purchaseStatus.COMPLETED,
    statusTimeline: timeline,
  };
};

const createPR = async (payload) => {
  try {
    const data = handleValidation(payload, prSchema);
    if (!data) return;

    const prNumber = await getNextPurchaseRequestId(data.tenantId);
    const isApprovalNeed = await isApprovalNeeded(data.tenantId, "prApproval");

    const items = data.items.map((item) => {
      const result = {
        itemName: item.itemName,
        itemId: item.itemId,
        itemCode: item.itemCode,
        categoryId: item.categoryId,
        subcategoryId: item.subcategoryId,
        categoryName: item.categoryName,
        subcategoryName: item.subcategoryName,
        pkg: item.pkg,
        quantity: item.quantity,
        pkgUOM: item.pkgUOM || null,
        purchaseUOM: item.purchaseUOM,
        vendor: item.vendor,
        remarks: item.remarks || null,
        hsnCode: item.hsnCode,
        contractType: item.contractType,
        contractPrice: item.contractPrice,
        contractId: item.contractId || null,
        inStock: item.inStock,
        inclTax: item.inclTax,
        taxRate: item.taxRate,
        unitCost: rupeeToPaise(item.unitCost),
        ...formatAmountToPaise(item),
      };
      return result;
    });

    let requestData = {
      prNumber,
      deliveryDate: FD.toFirestore(data.deliveryDate, TIME_OPTION.START),
      location: data.location,
      inventoryLocation: data.inventoryLocation,
      requestedBy: data.requestedBy,
      lastUpdatedTime: FD.now(),
      tenantId: data.tenantId,
      vendorType: data.vendorType,
      items,
      statusTimeline: [],
      ...formatAmountToPaise(data),
    };

    const { status, statusTimeline } = buildStatusTimeline(
      data,
      isApprovalNeed
    );

    requestData.status = status;
    requestData.statusTimeline = statusTimeline;

    let result = await savePurchaseRequest(requestData);

    // format data to return
    result.items = items.map((item) => ({
      ...item,
      ...formatAmountToRupee(item),
      unitCost: paiseToRupee(item.unitCost),
    }));
    result.deliveryDate = new Date(
      FD.toFormattedDate(result.deliveryDate, DATE_FORMAT.DATE_ONLY)
    );

    result = { ...result, ...formatAmountToRupee(result) };

    return result;
  } catch (err) {
    throw Error(err.message);
  }
};

const formatAmountToPaise = (item) => {
  return {
    grossAmount: rupeeToPaise(item.grossAmount),
    totalDiscount: rupeeToPaise(item.totalDiscount),
    netAmount: rupeeToPaise(item.netAmount),
    charges: item.charges,
    totalChargeAmount: rupeeToPaise(item.totalChargeAmount),
    taxes: item.taxes,
    totalTaxAmount: rupeeToPaise(item.totalTaxAmount),
    totalAmount: rupeeToPaise(item.totalAmount),
    totalCess: rupeeToPaise(item.totalCess),
    totalFocAmount: rupeeToPaise(item.totalFocAmount),
    roundoff: rupeeToPaise(item.roundoff),
  };
};

const formatAmountToRupee = (item, format = true) => {
  return {
    grossAmount: format ? paiseToRupee(item.grossAmount) : item.grossAmount,
    totalDiscount: format
      ? paiseToRupee(item.totalDiscount)
      : item.totalDiscount,
    netAmount: format ? paiseToRupee(item.netAmount) : item.netAmount,
    totalChargeAmount: format
      ? paiseToRupee(item.totalChargeAmount)
      : item.totalChargeAmount,
    totalTaxAmount: format
      ? paiseToRupee(item.totalTaxAmount)
      : item.totalTaxAmount,
    totalAmount: format ? paiseToRupee(item.totalAmount) : item.totalAmount,
    totalFocAmount: format
      ? paiseToRupee(item.totalFocAmount)
      : item.totalFocAmount,
    totalCess: format ? paiseToRupee(item.totalCess) : item.totalCess,
    charges: item.charges,
    taxes: item.taxes,
    roundoff: format ? paiseToRupee(item.roundoff) : item.roundoff,
  };
};

const getPRById = async (id, format = true) => {
  try {
    const data = await getPurchaseRequestById(id);
    if (!data) return null;

    const items = data.items.map((item) => ({
      itemName: item.itemName,
      itemId: item.itemId,
      itemCode: item.itemCode,
      categoryId: item.categoryId,
      subcategoryId: item.subcategoryId,
      categoryName: item.categoryName,
      subcategoryName: item.subcategoryName,
      pkg: item.pkg,
      quantity: item.quantity,
      pkgUOM: item.pkgUOM,
      purchaseUOM: item.purchaseUOM,
      vendor: item.vendor,
      remarks: item.remarks,
      hsnCode: item.hsnCode,
      contractType: item.contractType,
      contractPrice: item.contractPrice,
      contractId: item.contractId,
      contractNumber: item.contractNumber || null,
      inStock: item.inStock,
      inclTax: item.inclTax,
      taxRate: item.taxRate,
      unitCost: format ? paiseToRupee(item.unitCost) : item.unitCost,
      ...formatAmountToRupee(item, format),
    }));

    return {
      id: data.id,
      prNumber: data.prNumber,
      deliveryDate: format
        ? FD.toFormattedDate(data.deliveryDate, DATE_FORMAT.DATE_ONLY)
        : data.deliveryDate,
      location: data.location,
      inventoryLocation: data.inventoryLocation,
      requestedBy: data.requestedBy,
      status: data.status,
      lastUpdatedTime: format
        ? FD.toFormattedDate(data.lastUpdatedTime)
        : data.lastUpdatedTime,
      tenantId: data.tenantId,
      vendorType: data.vendorType,
      items,
      statusTimeline: format
        ? data.statusTimeline.map((timeline) => ({
            ...timeline,
            time: FD.toFormattedDate(timeline.time),
          }))
        : data.statusTimeline,
      remarks: data.remarks || null,
      ...formatAmountToRupee(data, format),
    };
  } catch (err) {
    throw Error(err.message);
  }
};

const updatePR = async (id, payload) => {
  try {
    const data = handleValidation(payload, prSchema);
    if (!data) return;

    if (!data.prNumber)
      data.prNumber = await getNextPurchaseRequestId(data.tenantId);

    const isApprovalNeed = await isApprovalNeeded(data.tenantId, "prApproval");

    const tsNow = FD.now();

    const items = data.items.map((item) => {
      const result = {
        ...item,
        unitCost: rupeeToPaise(item.unitCost),
        vendor: item.vendor,
        ...formatAmountToPaise(item),
      };
      return result;
    });

    const updatedData = {
      ...data,
      items,
      lastUpdatedTime: tsNow,
      deliveryDate: FD.toFirestore(data.deliveryDate, TIME_OPTION.START),
      statusTimeline: data.statusTimeline.map((timeline) => ({
        ...timeline,
        time: FD.toFirestore(timeline.time),
      })),
      ...formatAmountToPaise(data),
    };

    if (data.status === purchaseStatus.SUBMITTED) {
      updatedData.status = purchaseStatus.SUBMITTED;
      updatedData.statusTimeline.push({
        name: purchaseStatus.SUBMITTED,
        time: tsNow,
        by: data.updatedBy,
      });

      if (!isApprovalNeed) {
        updatedData.status = purchaseStatus.APPROVED;
        updatedData.statusTimeline.push({
          name: purchaseStatus.APPROVED,
          time: tsNow,
          by: data.updatedBy,
        });
      }
    }

    if (data.status === purchaseStatus.COMPLETED) {
      updatedData.status = purchaseStatus.COMPLETED;
      updatedData.statusTimeline.push({
        name: purchaseStatus.COMPLETED,
        time: tsNow,
        by: data.updatedBy,
      });
    }

    await updatePurchaseRequestById(id, updatedData);
  } catch (err) {
    throw new Error(`Service Error (updatePurchaseRequest): ${err.message}`);
  }
};

const completePR = async (id, payload) => {
  try {
    payload.status = purchaseStatus.COMPLETED;
    payload.statusTimeline.push({
      name: purchaseStatus.COMPLETED,
      time: FD.now(),
      by: payload.updatedBy,
    });
    await updatePurchaseRequestById(id, payload);
  } catch (err) {
    throw Error(err.message);
  }
};

const approvePR = async (id, approvedBy) => {
  try {
    const data = await getPurchaseRequestById(id);
    if (!data) return null;

    const tsNow = FD.now();

    const updatedTimeline = [
      ...(data.statusTimeline || []),
      {
        name: purchaseStatus.APPROVED,
        time: tsNow,
        by: approvedBy,
      },
    ];

    await updatePurchaseRequestById(id, {
      status: purchaseStatus.APPROVED,
      statusTimeline: updatedTimeline,
      lastUpdatedTime: tsNow,
    });

    return true;
  } catch (err) {
    throw Error(err.message);
  }
};

const rejectPR = async (id, rejectedBy, rejectedReason) => {
  try {
    const data = await getPurchaseRequestById(id);
    if (!data) return null;

    const tsNow = FD.now();

    const updatedTimeline = [
      ...(data.statusTimeline || []),
      {
        name: purchaseStatus.REJECTED,
        time: tsNow,
        by: rejectedBy,
      },
    ];

    await updatePurchaseRequestById(id, {
      status: purchaseStatus.REJECTED,
      statusTimeline: updatedTimeline,
      lastUpdatedTime: tsNow,
      rejectedReason,
      activeStatus: false,
    });

    return true;
  } catch (err) {
    throw Error(err.message);
  }
};

const convertPO = async (payload) => {
  try {
    // Group items by vendor
    const vendorMap = {};
    for (const item of payload.items) {
      const vendorId = item.vendor.id;
      if (!vendorMap[vendorId]) {
        vendorMap[vendorId] = {
          items: [],
        };
      }

      const { vendor, ...rest } = item;
      vendorMap[vendorId].items.push(rest);
    }

    const tsNow = FD.now();

    const purchaseOrders = [];
    for (const vendorId in vendorMap) {
      const { items } = vendorMap[vendorId];
      const [poNumber, vendor] = await Promise.all([
        getNextPurchaseOrderId(payload.tenantId),
        getVendorById(payload.tenantId, vendorId),
      ]);

      const requestData = {
        poNumber,
        vendor,
        location: payload.location,
        inventoryLocation: payload.inventoryLocation,
        items,
        status: purchaseStatus.DRAFT,
        statusTimeline: [
          {
            name: purchaseStatus.DRAFT,
            time: tsNow,
            by: payload.requestedBy,
          },
        ],
        prNumber: payload.prNumber,
        requestedBy: payload.requestedBy,
        tenantId: payload.tenantId,
        requestedTime: tsNow,
        lastUpdatedTime: tsNow,
        deliveryDate: payload.deliveryDate,
        grossAmount: payload.grossAmount,
        totalDiscount: payload.totalDiscount,
        netAmount: payload.netAmount,
        totalChargeAmount: payload.totalChargeAmount,
        totalTaxAmount: payload.totalTaxAmount,
        totalAmount: payload.totalAmount,
        totalFocAmount: payload.totalFocAmount,
        totalCess: payload.totalCess,
        charges: payload.charges,
        taxes: payload.taxes,
        roundoff: payload.roundoff,
        remarks: payload.remarks || null,
      };

      const cart = { ...cartHelper.NewCart("pr"), ...requestData };

      const { type, ...cartResult } = cartHelper.Calculate(cart);

      const validatedData = handleValidation(
        { ...requestData, ...cartResult },
        poSchema
      );
      if (!validatedData) return;

      purchaseOrders.push(validatedData);
    }

    return await savePurchaseOrder(purchaseOrders);
  } catch (err) {
    throw Error(err.message);
  }
};

const createPO = async (payload) => {
  try {
    const isApprovalNeed = await isApprovalNeeded(
      payload.tenantId,
      "poApproval"
    );

    // Group items by vendor
    const vendorMap = {};
    for (const item of payload.items) {
      const vendorId = item.vendor.id;
      if (!vendorMap[vendorId]) {
        vendorMap[vendorId] = {
          items: [],
        };
      }

      const { vendor, ...rest } = item;
      vendorMap[vendorId].items.push(rest);
    }
    const tsNow = FD.now();
    const purchaseOrders = [];

    for (const vendorId in vendorMap) {
      const { items } = vendorMap[vendorId];
      const [poNumber, vendor] = await Promise.all([
        getNextPurchaseOrderId(payload.tenantId),
        getVendorById(payload.tenantId, vendorId),
      ]);

      const requestData = {
        poNumber,
        vendor: {
          ...vendor,
          paymentTerms: payload.paymentTerms,
          poTerms: payload.poTerms,
        },
        location: payload.location,
        inventoryLocation: payload.inventoryLocation,
        items: items.map((item) => ({
          ...item,
          unitCost: rupeeToPaise(item.unitCost),
          ...formatAmountToPaise(item),
        })),
        status: purchaseStatus.DRAFT,
        statusTimeline: [
          {
            name: purchaseStatus.DRAFT,
            time: tsNow,
            by: payload.requestedBy,
          },
        ],
        prNumber: payload.prNumber,
        requestedBy: payload.requestedBy,
        tenantId: payload.tenantId,
        requestedTime: tsNow,
        lastUpdatedTime: tsNow,
        deliveryDate: FD.toFirestore(payload.deliveryDate, TIME_OPTION.START),
        remarks: payload.remarks,
        ...formatAmountToPaise(payload),
      };

      if (payload.action == purchaseStatus.SUBMITTED) {
        requestData.status = purchaseStatus.SUBMITTED;
        requestData.statusTimeline.push({
          name: purchaseStatus.SUBMITTED,
          time: tsNow,
          by: payload.requestedBy,
        });
      }

      if (payload.action == purchaseStatus.SUBMITTED && !isApprovalNeed) {
        requestData.status = purchaseStatus.APPROVED;
        requestData.statusTimeline.push({
          name: purchaseStatus.APPROVED,
          time: tsNow,
          by: payload.requestedBy,
        });
      }

      const validatedData = handleValidation(requestData, poSchema);
      if (!validatedData) return;

      purchaseOrders.push(validatedData);
    }

    return await savePurchaseOrder(purchaseOrders);
  } catch (err) {
    console.log(err, "errrrrrr");
    throw Error(err.message);
  }
};

const approvePO = async (id, approvedBy) => {
  try {
    const data = await getPOById(id);
    if (!data) return null;

    const updatedTimeline = [
      ...(data.statusTimeline || []),
      {
        name: purchaseStatus.APPROVED,
        time: FD.now(),
        by: approvedBy,
      },
    ];

    await updatePO(id, {
      items: data.items,
      status: purchaseStatus.APPROVED,
      statusTimeline: updatedTimeline,
    });

    return true;
  } catch (err) {
    throw Error(err.message);
  }
};

const rejectPO = async (id, rejectedBy, rejectedReason) => {
  try {
    const data = await getPOById(id);
    if (!data) return null;

    const updatedTimeline = [
      ...(data.statusTimeline || []),
      {
        name: purchaseStatus.REJECTED,
        time: FD.now(),
        by: rejectedBy,
      },
    ];

    await updatePO(id, {
      items: data.items,
      status: purchaseStatus.REJECTED,
      statusTimeline: updatedTimeline,
      rejectedReason,
      activeStatus: false,
    });

    return true;
  } catch (err) {
    throw Error(err.message);
  }
};

const getPurchaseOrderById = async (id) => {
  try {
    const data = await getPOById(id);
    if (!data) return null;

    const items = data.items.map((item) => ({
      itemName: item.itemName,
      itemId: item.itemId,
      itemCode: item.itemCode,
      categoryId: item.categoryId,
      subcategoryId: item.subcategoryId,
      categoryName: item.categoryName,
      subcategoryName: item.subcategoryName,
      pkg: item.pkg,
      quantity: item.quantity,
      receivedQty: item.receivedQty,
      pkgUOM: item.pkgUOM,
      purchaseUOM: item.purchaseUOM,
      remarks: item.remarks,
      hsnCode: item.hsnCode,
      contractType: item.contractType,
      contractPrice: item.contractPrice,
      contractId: item.contractId,
      contractNumber: item.contractNumber,
      inStock: item.inStock,
      inclTax: item.inclTax,
      taxRate: item.taxRate,
      unitCost: paiseToRupee(item.unitCost),
      ...formatAmountToRupee(item),
    }));

    return {
      id: data.id,
      prNumber: data.prNumber,
      poNumber: data.poNumber,
      deliveryDate: FD.toFormattedDate(
        data.deliveryDate,
        DATE_FORMAT.DATE_ONLY
      ),
      location: data.location,
      inventoryLocation: data.inventoryLocation,
      requestedBy: data.requestedBy,
      status: data.status,
      lastUpdatedTime: FD.toFormattedDate(data.lastUpdatedTime),
      tenantId: data.tenantId,
      vendorType: data.vendorType,
      items,
      statusTimeline: data.statusTimeline.map((timeline) => ({
        ...timeline,
        time: FD.toFormattedDate(timeline.time),
      })),
      vendor: data.vendor,
      remarks: data.remarks,
      requestedTime: FD.toFormattedDate(data.requestedTime),
      invoiceDate: FD.toFormattedDate(data.invoiceDate, DATE_FORMAT.DATE_ONLY),
      invoiceNumber: data.invoiceNumber,
      grnDate: FD.toFormattedDate(data.grnDate, DATE_FORMAT.DATE_ONLY),
      ...formatAmountToRupee(data),
    };
  } catch (err) {
    throw Error(err.message);
  }
};

const updatePurchaseOrder = async (id, payload) => {
  try {
    const data = handleValidation(payload, poSchema);
    if (!data) return;

    const isApprovalNeed = await isApprovalNeeded(
      payload.tenantId,
      "poApproval"
    );

    const tsNow = FD.now();

    const items = payload.items.map((item) => {
      return {
        ...item,
        unitCost: rupeeToPaise(item.unitCost),
        ...formatAmountToPaise(item),
      };
    });

    const updatedData = {
      ...payload,
      items,
      deliveryDate: FD.toFirestore(payload.deliveryDate),
      statusTimeline: payload.statusTimeline.map((timeline) => ({
        ...timeline,
        time: FD.toFirestore(timeline.time),
      })),
      ...formatAmountToPaise(payload),
    };

    if (payload.status === purchaseStatus.SUBMITTED) {
      updatedData.status = purchaseStatus.SUBMITTED;
      updatedData.statusTimeline.push({
        name: purchaseStatus.SUBMITTED,
        time: tsNow,
        by: payload.updatedBy,
      });

      if (!isApprovalNeed) {
        updatedData.status = purchaseStatus.APPROVED;
        updatedData.statusTimeline.push({
          name: purchaseStatus.APPROVED,
          time: tsNow,
          by: payload.updatedBy,
        });
      }
    }

    if (payload.status === purchaseStatus.COMPLETED) {
      updatedData.status = purchaseStatus.COMPLETED;
      updatedData.statusTimeline.push({
        name: purchaseStatus.COMPLETED,
        time: tsNow,
        by: payload.updatedBy,
      });
    }

    await updatePO(id, updatedData);
  } catch (err) {
    throw Error(err.message);
  }
};

module.exports = {
  receivePO,
  aggregatePurchaseRequests,
  aggregatePurchaseOrders,
  createPR,
  getPRById,
  updatePR,
  approvePR,
  rejectPR,
  createPO,
  approvePO,
  rejectPO,
  getPurchaseOrderById,
  convertPO,
  completePR,
  updatePurchaseOrder,
};
