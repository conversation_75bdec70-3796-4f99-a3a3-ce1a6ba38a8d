// services/grnService.js

const {
  listGRNs,
  getGRNById,
  getGRNByNumber,
  updateAttachments,
  deleteAttachment,
} = require("@/repositories/grnRepo");
const {
  getLedgersByGRNId,
  findGrnItemPricesBetween,
  findLastGrnItemPrice,
} = require("@/repositories/stockLedgerRepo");
const { ResultTypes } = require("@/defs/resultTypeDefs");
const { paiseToRupee } = require("@/utils/money");
const {
  DATE_FORMAT,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");
const {
  getDefaultWorkAreaByLocationId,
} = require("@/repositories/locationRepo");

/**
 * Builds a detailed GRN document from a given GRN document.
 * Fetches ledger entries linked to the given GRN and transforms them into an array of items.
 * @param {object} grnDoc - GRN document to build the detailed document from.
 * @returns {Promise<object>} - Detailed GRN document with items or null if grnDoc is null.
 */
async function buildGRNDetail(grnDoc) {
  if (!grnDoc) return null;

  // Fetch ledger entries linked to this GRN
  const ledgers = await getLedgersByGRNId(grnDoc.grnId, grnDoc.tenantId);

  const items = ledgers.map((l) => {
    const data = {
      itemId: l.itemId,
      itemCode: l.itemCode,
      itemName: l.itemName,
      pkg: l.pkg,
      remarks: l.remarks,
      orderedQty: l.orderedQty,
      receivedQty: l.qty,
      uom: l.pkg?.id !== "default" ? l.pkg.name : l.countingUOM,
      unitCost: paiseToRupee(l.unitCost),
      totalAmount: paiseToRupee(l.totalCost),
      totalTaxAmount: paiseToRupee(l.taxAmount),
      totalDiscount: paiseToRupee(l.discount),
      taxRate: l.taxRate,
      foc: l.foc,
      totalCess: paiseToRupee(l.cess),
    };

    data.netAmount = data.unitCost * data.receivedQty - data.totalDiscount;

    return data;
  });

  return {
    ...grnDoc,
    items,
  };
}

/**
 * Retrieves a single GRN document by its ID.
 * - Fetches the GRN document from Firestore.
 * - Builds the detailed GRN document by fetching the linked ledger entries and transforming them into an array of items.
 * @param {string} tenantId - Tenant ID to filter GRNs by.
 * @param {string} grnId - Unique identifier of the GRN document to retrieve.
 * @returns {Promise<object>} - Detailed GRN document with items or null if not found.
 */
async function getGrnByID(grnId, tenantId) {
  const grnDoc = await getGRNById(grnId, tenantId);
  return grnDoc;
  // return await buildGRNDetail(grnDoc);
}

/**
 * Retrieves a single GRN document by its number.
 * - Fetches the GRN document from Firestore.
 * - Builds the detailed GRN document by fetching the linked ledger entries and transforming them into an array of items.
 * @param {string} tenantId - Tenant ID to filter GRNs by.
 * @param {string} grnNumber - Unique GRN number of the GRN document to retrieve.
 * @returns {Promise<object>} - Detailed GRN document with items or null if not found.
 */
async function getGrnByNumber(tenantId, grnNumber) {
  const grnDoc = await getGRNByNumber(tenantId, grnNumber);
  return await buildGRNDetail(grnDoc);
}

/**
 * Lists GRN summaries for the given tenantId, with optional filters for locationId, startDate, and endDate.
 * - Returns a list of GRN summaries in the given resultType (either "json" or "excel").
 * - If resultType is "excel", sets the Content-Disposition and Content-Type headers to return the GRN summaries as an Excel file.
 * @param {object} filters - Filters to apply when listing GRNs. Supports locationId, startDate, and endDate.
 * @param {string} resultType - Type of result to return. Either "json" or "excel".
 * @returns {Promise<object|Buffer>} - List of GRN summaries or Excel file containing the GRN summaries.
 */
async function listGrnSummaries(filters, resultType) {
  const grns = await listGRNs(filters);

  const result = grns.map((grn) => ({
    ...grn,
    grnDate: FD.toFormattedDate(grn.grnDate, DATE_FORMAT.DATE_ONLY),
    invoiceDate: FD.toFormattedDate(grn.invoiceDate, DATE_FORMAT.DATE_ONLY),
    totalAmount: paiseToRupee(grn.totalAmount),
    createdAt: FD.toFormattedDate(grn.createdAt),
  }));

  if (resultType === ResultTypes.EXCEL.toLowerCase()) {
    const workbook = new ExcelJS.Workbook();
    const sheet = workbook.addWorksheet("GRNs");

    sheet.columns = [
      { header: "GRN ID", key: "grnId" },
      { header: "GRN Number", key: "grnNumber" },
      { header: "Location", key: "locationName" },
      { header: "Vendor", key: "vendorName" },
      { header: "Total Value", key: "totalValue" },
      { header: "Created At", key: "createdAt" },
    ];

    result.forEach((grn) => sheet.addRow(grn));

    return await workbook.xlsx.writeBuffer();
  }

  return result;
}

async function fetchGrnItemPricesByPeriod({
  tenantId,
  itemId,
  inventoryLocationId,
  pkgId,
}) {
  return findGrnItemPricesBetween({
    tenantId,
    itemId,
    inventoryLocationId,
    pkgId,
  });
}

async function fetchLastGrnItemPrice({
  tenantId,
  itemId,
  inventoryLocationId,
  pkgId,
}) {
  return findLastGrnItemPrice({
    tenantId,
    itemId,
    inventoryLocationId,
    pkgId,
  });
}

const updateGrnAttachments = async (id, attachments) => {
  try {
    const updatedAttachments = await updateAttachments(id, attachments);
    return {
      success: true,
      message: `Updated successfully`,
      attachments: updatedAttachments,
    };
  } catch (err) {
    throw new Error(err.message);
  }
};

const deleteGrnAttachment = async (id, filePath) => {
  try {
    if (!filePath) throw new Error("File path required");

    const updatedAttachments = await deleteAttachment(id, filePath);

    return {
      success: true,
      message: `File deleted successfully`,
      attachments: updatedAttachments,
    };
  } catch (err) {
    throw new Error(err.message);
  }
};

module.exports = {
  getGrnByID,
  getGrnByNumber,
  listGrnSummaries,
  fetchGrnItemPricesByPeriod,
  updateGrnAttachments,
  deleteGrnAttachment,
  fetchLastGrnItemPrice,
};
