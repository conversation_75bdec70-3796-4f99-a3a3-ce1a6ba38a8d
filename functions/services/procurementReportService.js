const { REPORTS, REPORT_INFORMATION, AGGREGATE_TYPES } = require("@/defs/reportDefs");
const {
  validateAndPrepareFilters,
  requiresLedgerData,
  filterItem,
  constructColumns,
} = require("@/helpers/reportHelper");
const { ResultType } = require("@/helpers/render");
const { createXlsxReport } = require("@/helpers/xlsxReportUtility");
const {
  DATE_FORMAT,
  FirestoreDateHelper: FD,
} = require("@/helpers/dateHelper");
const { paiseToRupee } = require("@/utils/money");

const { fetchGRNReportData } = require("@/repositories/procurementReportRepo");

/**
 * NewProcurementReport
 * --------------------
 * Identifies collection, determines aggregateType,
 * fetches snapshot immediately on construction,
 * and exposes result scaffolding + context.
 */
class NewProcurementReport {
  #headers = [];
  #aggregateId = null;
  #detailedReport = false;
  #format;
  constructor(tenantId, payload, reportType) {
    if (!tenantId) throw new Error("Tenant ID is required");

    const reportInfo = REPORT_INFORMATION[reportType];
    if (!reportInfo) throw new Error("Invalid report type");

    this.#headers = reportInfo.headers;
    this.#aggregateId = reportInfo.aggregateId;

    const { filters = {}, columns = [], options } = payload || {};

    // Expose everything upfront
    this._options = {
      _filters: validateAndPrepareFilters(tenantId, filters),
      columns,
      reportType,
      splitBy: options?.splitBy,
    };

    // Create default result skeleton
    this.result = {
      id: reportInfo.id,
      name: reportInfo.name,
      headers: this.#headers,
      tenantId,
      data: [],
      totalRow: {
        grossAmount: 0,
        totalDiscount: 0,
        netAmount: 0,
        totalChargeAmount: 0,
        totalTaxAmount: 0,
        totalAmount: 0,
        totalCess: 0,
        totalFocAmount: 0,
      },
      payload,
      _meta: null,
      expand: false
    };

    // fetch immediately (sync-like)
    // store the promise — controller will await it
    this.#format = this.#formGRN;
    this.#detailedReport = this.#identify(
      this._options._filters,
      reportInfo.id
    );
    if (this.#detailedReport) {
      this.#format = this.#formDetailedGRN;
    }
    this._snapPromise = fetchGRNReportData(tenantId, this._options._filters);
  }

  /**
   * Resolve Firestore snapshot
   */
  async snap() {
    this._snap = await this._snapPromise;
    return this._snap;
  }

  async get() {
    const res = [];
    const snapshot = await this.snap();
    snapshot.docs.map((doc) => {
      const grn = doc.data();
      res.push(...this.#format(grn));
    });

    this.result.data = res.map((v) => {
      const row = {
        ...v,
        grossAmount: paiseToRupee(v.grossAmount),
        totalDiscount: paiseToRupee(v.totalDiscount),
        netAmount: paiseToRupee(v.netAmount),
        totalChargeAmount: paiseToRupee(v.totalChargeAmount),
        totalTaxAmount: paiseToRupee(v.totalTaxAmount),
        totalAmount: paiseToRupee(v.totalAmount),
        unitCost: paiseToRupee(v.unitCost),
        totalCess: paiseToRupee(v.totalCess),
        totalFocAmount: paiseToRupee(v.totalFocAmount)
      };

      // accumulate totals
      this.result.totalRow.grossAmount += row.grossAmount;
      this.result.totalRow.totalDiscount += row.totalDiscount;
      this.result.totalRow.netAmount += row.netAmount;
      this.result.totalRow.totalChargeAmount += row.totalChargeAmount;
      this.result.totalRow.totalTaxAmount += row.totalTaxAmount;
      this.result.totalRow.totalAmount += row.totalAmount;
      this.result.totalRow.totalCess += row.totalCess;
      this.result.totalRow.totalFocAmount += row.totalFocAmount;

      return row;
    });
    return this.result.data;
  }

  generate(resultType) {
    const aggregateId = this.#aggregateId;
    const headers = this.#headers;
    const options = this._options;
    const result = this.result;

    // Step 1: Aggregate when a main aggregateId is present
    if (aggregateId) {
      const aggregateConfig = AGGREGATE_TYPES[aggregateId];
      if (!aggregateConfig) throw new Error(`Invalid aggregate type: ${aggregateId}`);
      this.#aggregate(aggregateConfig);
    }

    // Step 2: split-by logic triggers a second aggregation pass
    const splitBy = options.splitBy;
    let splitByColumn = null;
    if (splitBy) {
      result.expand = true;
      const aggregateConfig = AGGREGATE_TYPES[splitBy] || {};
      if (!aggregateConfig) throw new Error(`Invalid split-by type: ${splitBy}`);
      splitByColumn = aggregateConfig.label;
      this.#aggregate(aggregateConfig, true);
    }

    // Step 3: Build final headers
    result.headers = constructColumns(
      headers,
      options.columns,
      result._meta,
      splitByColumn,
      resultType
    );

    return this.#output(resultType);
  }

  #output(resultType) {
    switch (resultType) {
      case ResultType.EXCEL:
        return createXlsxReport(this.result);
      default:
        return this.result;
    }
  }

  #getAmt(data) {
    return {
      grossAmount: data.grossAmount,
      totalDiscount: data.totalDiscount,
      netAmount: data.netAmount,
      totalChargeAmount: data.totalChargeAmount,
      totalTaxAmount: data.totalTaxAmount,
      totalAmount: data.totalAmount,
      totalCess: data.totalCess,
      totalFocAmount: data.totalFocAmount,
    };
  }

  #getItemAmt(data) {
    let res = {
      grossAmount: data.grossAmount,
      totalDiscount: data.totalDiscount,
      totalCess: data.totalCess,
      totalTaxAmount: data.totalTaxAmount,
      totalChargeAmount: data.totalChargeAmount,
      totalAmount: data.totalAmount - data.totalFocAmount,
      totalFocAmount: data.totalFocAmount,
      netAmount: data.netAmount,
    };
    return res;
  }

  #formGRN(grn) {
    const result = {
      grnNumber: grn.grnNumber,
      poNumber: grn.poNumber,
      invoiceNumber: grn.invoiceNumber,
      invoiceDate: FD.toFormattedDate(grn.invoiceDate, DATE_FORMAT.DATE_ONLY),
      vendorName: grn.vendorName,
      // vendorId: grn.vendorId,
      locationId: grn.location?.id,
      locationName: grn.location?.name,
      inventoryLocationId: grn.inventoryLocation?.id,
      inventoryLocationName: grn.inventoryLocation?.name,
      date: FD.toFormattedDate(grn.grnDate, DATE_FORMAT.DATE_ONLY), // change date grn/invoice/created
      grnDate: FD.toFormattedDate(grn.grnDate, DATE_FORMAT.DATE_ONLY),
      createdAt: FD.toFormattedDate(grn.createdAt),
      createdByName: grn.createdBy?.name || grn.receivedByName,
      ...this.#getAmt(grn),
    };
    return [result];
  }

  #filter(item) {
    return filterItem(this._options._filters, item);
  }

  #formDetailedGRN(grn) {
    const results = [];
    const [grnData] = this.#formGRN(grn);
    grn.items?.forEach((item) => {
      if (!this.#filter(item)) return false;
      const result = {
        ...grnData,
        itemId: item.itemId,
        itemName: item.itemName,
        itemCode: item.itemCode,
        hsnCode: item.hsnCode || "-",
        categoryId: item.categoryId,
        categoryName: item.categoryName || item.categoryId,
        subCategoryId: item.subcategoryId,
        subCategoryName: item.subcategoryName || item.subcategoryId,
        // pkg: item.pkg ? item.pkg.name : item.purchaseUOM,
        pkg: item.pkg?.id === "default" ? item.purchaseUOM : item.pkg?.name,
        pkgId: item.pkg?.id,
        qty: item.qtyReceived,
        unitCost: item.unitCost,
        ...this.#getItemAmt(item),
      };
      results.push(result);
    });
    return results;
  }

  #aggregate({ id, label, columns }, detailed = false) {
    const map = new Map();
    const result = this.result.data;
    this.result.data = [];

    const extractFields = (data, columns = []) => {
      const fields = {};
      for (const column of columns) {
        fields[column] = data[column];
      }
      return fields;
    };

    const getKey = (data, id) => {
      if (Array.isArray(id)) {
        return id.map((key) => data[key]).join("|");
      }
      return data[id];
    }

    result.forEach((data) => {
      const key = getKey(data, id);
      const existing = map.get(key) || {
        id: key,
        [label]: data[label],
        ...extractFields(data, columns),
        grossAmount: 0,
        totalDiscount: 0,
        netAmount: 0,
        totalChargeAmount: 0,
        totalTaxAmount: 0,
        totalAmount: 0,
        totalFocAmount: 0,
        totalCess: 0,
        qty: 0,
        totalUnitCost: 0,
        unitCost: 0,
        entries: 0,
        subItems: [],
      };
      existing.grossAmount += data.grossAmount;
      existing.totalDiscount += data.totalDiscount;
      existing.netAmount += data.netAmount;
      existing.totalChargeAmount += data.totalChargeAmount;
      existing.totalTaxAmount += data.totalTaxAmount;
      existing.totalFocAmount += data.totalFocAmount;
      existing.totalCess += data.totalCess;
      existing.totalAmount += data.totalAmount;
      existing.entries += 1;

      // item specific
      existing.qty += data.qty || 0;
      existing.totalUnitCost += data.unitCost || 0;
      existing.unitCost = Number(existing.totalUnitCost / existing.entries).toFixed(2);

      if (detailed) {
        existing.subItems.push(data);
      }

      map.set(key, existing);
    });

    this.result.data = Array.from(map.values());
  }

  /**
   * Identify collection and fetch function.
   * @private
   */
  #identify(filters, reportType) {
    const ledgerReports = [
      REPORTS.DETAILED_GRN,
      REPORTS.CATEGORY_WISE_GRN,
      REPORTS.SUB_CATEGORY_WISE_GRN,
      REPORTS.ITEM_WISE_GRN,
    ];
    return ledgerReports.includes(reportType) || requiresLedgerData(filters);
  }
}

/**
 * GRN Summary Report
 * ------------------
 * Summarizes GRN details like invoice, vendor, total value, etc.
 */
const getGRNReport = async (tenantId, payload, resultType) => {
  const report = new NewProcurementReport(tenantId, payload, REPORTS.GRN);
  await report.get();
  return report.generate(resultType);
};

/**
 * Detailed GRN Report
 * -------------------
 * Provides line-item level GRN data with quantities and values.
 */
const getDetailedGRNReport = async (tenantId, payload, resultType) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.DETAILED_GRN
  );
  await report.get();
  return report.generate(resultType);
};

/**
 * Location Purchase Report
 * ------------------------
 * Summarizes purchase values per location.
 */
const getLocationWiseGRNReport = async (tenantId, payload, resultType) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.LOCATION_WISE_GRN
  );
  await report.get();
  return report.generate(resultType, true);
};

/**
 * Vendor Purchase Report
 * ----------------------
 * Aggregates purchases grouped by vendor.
 */
const getVendorWiseGRNReport = async (tenantId, payload, resultType) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.VENDOR_WISE_GRN
  );
  await report.get();
  return report.generate(resultType, true);
};

/**
 * Category Purchase Report
 * ------------------------
 * Shows total purchase amount per category.
 */
const getCategoryWiseGRNReport = async (tenantId, payload, resultType) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.CATEGORY_WISE_GRN
  );
  await report.get();
  return report.generate(resultType, true);
};

/**
 * SubCategory Purchase Report
 * ------------------------
 * Shows total purchase amount per sub-category.
 */
const getSubCategoryWiseGRNReport = async (tenantId, payload, resultType) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.SUB_CATEGORY_WISE_GRN
  );
  await report.get();
  return report.generate(resultType, true);
};

/**
 * Item Purchase Report
 * --------------------
 * Item-wise total purchases and quantities.
 */
const getItemWiseGRNReport = async (tenantId, payload, resultType) => {
  const report = new NewProcurementReport(
    tenantId,
    payload,
    REPORTS.ITEM_WISE_GRN
  );
  await report.get();
  return report.generate(resultType, true);
};

/**
 * Daily Purchase Report
 * ---------------------
 * Date-wise total purchase summary.
 */
const getDailyGRNReport = async (tenantId, payload, resultType) => {
  const report = new NewProcurementReport(tenantId, payload, REPORTS.DAILY_GRN);
  await report.get();
  return report.generate(resultType, true);
};

module.exports = {
  getGRNReport,
  getDetailedGRNReport,
  getLocationWiseGRNReport,
  getVendorWiseGRNReport,
  getCategoryWiseGRNReport,
  getSubCategoryWiseGRNReport,
  getItemWiseGRNReport,
  getDailyGRNReport,
};
