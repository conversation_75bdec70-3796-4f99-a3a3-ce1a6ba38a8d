const admin = require("firebase-admin");
const db = admin.firestore();

const {
  fetchClosingItems,
  createClosing,
  fetchClosingData,
  getById,
} = require("@/repositories/closingRepo");
const { getNextClosingId } = require("./counterService");
const schema = require("@/schema/closingSchema");
const { handleValidation } = require("@/utils/validation");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");
const { getInventoryItemStocks } = require("@/repositories/stockRepo");
const { LedgerTypes } = require("@/defs/ledgerDefs");
const { creditStock, debitStock } = require("./stockService");

const getClosingItemsRequest = async (tenantId, locationId) => {
  const closingItems = await fetchClosingItems(tenantId, locationId);
  return closingItems;
};

const getClosingById = async (id) => {
  try {
    const data = await getById(id);
    if (!data) return null;
    return {
      ...data,
      closingDate: FD.toFormattedDate(data.closingDate),
      closedBy: {
        ...data.closedBy,
        time: FD.toFormattedDate(data.closedBy.time),
      },
    };
  } catch (err) {
    throw Error(err.message);
  }
};

const getClosingDataRequest = async (filters) => {
  const closingData = await fetchClosingData(filters);

  const result = closingData.map((doc) => ({
    closingNumber: doc.closingNumber,
    id: doc.id,
    closingDate: FD.toFormattedDate(doc.closingDate),
    locationName: doc.locationName,
    createdDate: FD.toFormattedDate(doc.closedBy.time),
    workAreaName: doc.workAreaName,
    closedBy: doc.closedBy.userName,
    stockCorrection: doc.stockCorrection,
  }));

  return result;
};

const createClosingRequest = async (data) => {
  try {
    const validatedData = handleValidation(data, schema);
    if (!validatedData) return;

    const closingNumber = await getNextClosingId(validatedData.tenantId);

    const createdClosing = await db.runTransaction(async (trans) => {
      if (validatedData.stockCorrection) {
        const items = await getInventoryItemStocks(
          validatedData.workAreaId,
          validatedData.items
        );

        // CREDIT ITEMS
        for (const item of items.filter((i) => i.closingQuantity > i.inStock)) {
          const qty = item.closingQuantity - item.inStock;
          await creditStock(
            {
              ledgerType: LedgerTypes.CLOSING_CREDIT,
              tenantId: validatedData.tenantId,
              locationId: validatedData.locationId,
              locationName: validatedData.locationName,
              inventoryLocationId: validatedData.workAreaId,
              inventoryLocationName: validatedData.workAreaName,
              itemId: item.itemId,
              itemCode: item.itemCode,
              itemName: item.itemName,
              qty,
              countingUOM: item.countingUOM,
              recipeUOM: item.recipeUOM || "",
              conversionFactor: item.conversionFactor || 1,
              unitCost: item.unitCost || 0,
              totalCost: (item.unitCost || 0) * qty,
              expiryDate: item.expiryDate || null,
              grnMeta: null,
              categoryId: item.categoryId,
              subcategoryId: item.subcategoryId,
              categoryName: item.categoryName,
              subcategoryName: item.subcategoryName,
              pkg: item.pkg,
              remarks: item.remarks || null,
            },
            trans
          );
        }

        // DEBIT ITEMS
        for (const item of items.filter((i) => i.closingQuantity < i.inStock)) {
          const qty = item.inStock - item.closingQuantity;
          await debitStock(
            {
              ledgerType: LedgerTypes.CLOSING_DEBIT,
              tenantId: validatedData.tenantId,
              locationId: validatedData.locationId,
              locationName: validatedData.locationName,
              inventoryLocationId: validatedData.workAreaId,
              inventoryLocationName: validatedData.workAreaName,
              itemId: item.itemId,
              itemCode: item.itemCode,
              itemName: item.itemName,
              qty,
              countingUOM: item.countingUOM,
              unitCost: item.unitCost || 0,
              totalCost: (item.unitCost || 0) * qty,
              expiryDate: item.expiryDate || null,
              grnMeta: null,
              categoryId: item.categoryId,
              subcategoryId: item.subcategoryId,
              categoryName: item.categoryName,
              subcategoryName: item.subcategoryName,
              pkg: item.pkg,
              remarks: item.remarks || null,
            },
            trans
          );
        }
      }

      const requestData = {
        ...validatedData,
        closingNumber,
      };

      const closingDoc = await createClosing(requestData, trans);
      return closingDoc;
    });

    return createdClosing;
  } catch (error) {
    console.error("Error in createClosingRequest:", error);
    throw new Error(error.message);
  }
};

module.exports = {
  getClosingItemsRequest,
  createClosingRequest,
  getClosingDataRequest,
  getClosingById,
};
