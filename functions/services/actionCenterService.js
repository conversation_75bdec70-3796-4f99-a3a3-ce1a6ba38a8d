const { COUNTER_TYPES } = require("@/defs/counterDefs");
const { PRIV_CODES } = require("@/defs/privilegeDefs");

const { getActivePOCount } = require("@/repositories/poRepo.js");
const { getActivePRCount } = require("@/repositories/prRepo.js");
const {
  getTransferReceiveCount,
  getTransferDispatchCount,
} = require("@/repositories/transferRepo.js");

const { getPOByNumber } = require("@/repositories/poRepo");
const { getPRByNumber } = require("@/repositories/prRepo");
const { getGRNByNumber } = require("@/repositories/grnRepo");
const { getTransferByNumber, getDispatchByNumber } = require("@/repositories/transferRepo");
const { getContractByNumber } = require("@/repositories/contractRepo");
const { getClosingByNumber } = require("@/repositories/closingRepo");

/**
* Returns dashboard summary counts
* @param {string} tenantId
* @param {string} userId (optional, if required in repo functions)
* @returns {Promise<{active_pr: number, active_po: number, active_transfers: number, active_dispatch: number}>}
*/
exports.getSummary = async (tenantId) => {
  // Run all fetches in parallel
  const [pos, prs, transfersReceive, transfersDispatch] = await Promise.all([
    getActivePOCount(tenantId),
    getActivePRCount(tenantId), // implement this in prRepo
    getTransferReceiveCount(tenantId),
    getTransferDispatchCount(tenantId),
  ]);

  return {
    active_pr: prs,
    active_po: pos,
    active_transfers: transfersReceive,
    active_dispatch: transfersDispatch,
  };
};

/**
 * GLOBAL fetch map — function + privilege code from PRIV_CODES
 */
const fetchMap = {
  [COUNTER_TYPES.PURCHASE_ORDER.prefix]: { fn: getPOByNumber, privilege: PRIV_CODES.PUR_PO },
  [COUNTER_TYPES.PURCHASE_REQUEST.prefix]: { fn: getPRByNumber, privilege: PRIV_CODES.PUR_PR },
  [COUNTER_TYPES.GRN.prefix]: { fn: getGRNByNumber, privilege: PRIV_CODES.PUR_GRN },
  [COUNTER_TYPES.TRANSFER.prefix]: { fn: getTransferByNumber, privilege: PRIV_CODES.PUR_INDENT },
  [COUNTER_TYPES.DISPATCH.prefix]: { fn: getDispatchByNumber, privilege: PRIV_CODES.PUR_DISPATCH },
  [COUNTER_TYPES.CONTRACT.prefix]: { fn: getContractByNumber, privilege: PRIV_CODES.CONTRACT },
  [COUNTER_TYPES.CLOSING.prefix]: { fn: getClosingByNumber, privilege: PRIV_CODES.CLOSING }
};

/**
 * Searches for a document by its number across multiple modules.
 * @param {string} tenantId - Tenant identifier
 * @param {string} number - Number to search for, must be in the format of <prefix><numeric part> e.g., "PO1234"
 * @returns {Promise<{type: string, id: string, result: object}>}
 */
exports.searchByNumber = async (tenantId, number) => {
  console.log("searchByNumber called with", tenantId, number);

  // Validate number format: <PREFIX><4 digits>
  const match = number.match(/^([A-Za-z]+)(\d{4})$/);
  if (!match) {
    throw new Error(`Invalid number format: ${number}. Expected format: <PREFIX><4 digits> e.g., PO1234`);
  }

  const prefix = match[1].toUpperCase();

  const entry = fetchMap[prefix];
  if (!entry) {
    throw new Error(`Unknown or unsupported prefix: ${prefix}`);
  }

  // Check privilege
  //  @Todo: validate privilege
  // if (!checkPrivilege(entry.privilege)) {
  //   throw new Error(`User does not have access to ${prefix} module`);
  // }

  // Fetch data
  const resultData = await entry.fn(tenantId, number);

  if (!resultData) {
    return {
      type: prefix,
      id: null,
      result: null
    };
  }

  // @todo: check whether the fetched item is accessible based on tenant/location
  // e.g., if resultData.locationId !== tenant.allowedLocationId -> throw error or return null

  return {
    type: prefix,
    id: resultData.id || null,
    result: resultData
  };
};