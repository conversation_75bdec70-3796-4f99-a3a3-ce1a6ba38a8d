// src/services/masterService.js
const masterRepo = require("@/repositories/masterRepo");

async function getAllMasters(tenantId) {
  const [
    locations,
    inventoryLocations,
    { categories, subCategories },
    vendors,
    tags,
    taxes,
    houseUnits,
  ] = await Promise.all([
    masterRepo.getLocations(tenantId),
    masterRepo.getInventoryLocations(tenantId),
    masterRepo.getCategoriesAndSubCategories(tenantId),
    masterRepo.getVendors(tenantId),
    masterRepo.getTags(tenantId),
    masterRepo.getTaxes(tenantId),
    masterRepo.getHouseUnits(tenantId),
  ]);

  return {
    locations,
    inventoryLocations,
    categories,
    subCategories,
    vendors,
    tags,
    taxes,
    houseUnits,
  };
}

async function getInventoryMasters(tenantId) {
  return await masterRepo.getInventoryItems(tenantId);
}

module.exports = {
  getAllMasters,
  getInventoryMasters,
};
