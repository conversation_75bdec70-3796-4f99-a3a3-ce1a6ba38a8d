// services/dashboards/inventoryService.js

/**
 * Inventory Dashboard Service
 * ---------------------------
 * Returns tenant-specific inventory summary.
 */

exports.getSummary = async (tenantId, payload) => {
  // filters = req.body, tenantId = from route params
  // TODO: Replace this with actual DB queries
  return {
    "openingStock": 890000.00,
    "closingStock": 945500.00,
    "purchasesVendor": 410000.00,
    "ibtIn": 65000.00,
    "returnsFromWork": 18500.00,
    "indentConsumed": 380000.00,
    "ibtOut": 45000.00,
    "returnsVendor": 12000.00,
    "spoilageLoss": 7500.00,
    "activeSkus": 324,
    "netMovement": 55500.00,
    "turnoverEfficiency": 0.41,

    "financialOverview": [
      {
        "title": "Opening Stock",
        "count": 324,
        "amount": 890000.00,
        "countPer": 100,
        "amountPer": 100
      },
      {
        "title": "Purchases",
        "count": 290,
        "amount": 410000.00,
        "countPer": 89.5,
        "amountPer": 46.1
      },
      {
        "title": "IBT In",
        "count": 110,
        "amount": 65000.00,
        "countPer": 34.0,
        "amountPer": 7.2
      },
      {
        "title": "Returns To Vendor",
        "count": 28,
        "amount": 12000.00,
        "countPer": 8.6,
        "amountPer": 1.3
      },
      {
        "title": "Closing Stock",
        "count": 302,
        "amount": 945500.00,
        "countPer": 93.2,
        "amountPer": 106.2
      }
    ],

    "rupeeValueTracking": [
      { "date": "2024-10-01", "amount": 28000.00 },
      { "date": "2024-10-05", "amount": 32500.00 },
      { "date": "2024-10-10", "amount": 35500.00 },
      { "date": "2024-10-15", "amount": 31000.00 },
      { "date": "2024-10-20", "amount": 36500.00 },
      { "date": "2024-10-25", "amount": 45000.00 },
      { "date": "2024-10-27", "amount": 39000.00 }
    ],

    "varianceAction": [
      { "sku": "Chicken Breast", "system": 180, "physical": 165, "variance": -15, "amount": -2550.00 },
      { "sku": "Cheddar Cheese", "system": 95, "physical": 100, "variance": 5, "amount": 875.00 },
      { "sku": "Cooking Oil", "system": 240, "physical": 235, "variance": -5, "amount": -600.00 }
    ],

    "riskIntelligence": [
      { "sku": "Frozen Fish", "flag": "critical", "agingDays": 48, "qty": 32, "amount": 17200.00 },
      { "sku": "Paneer Cubes", "flag": "watch", "agingDays": 21, "qty": 50, "amount": 7200.00 }
    ],

    "topInvestmentItems": [
      { "sku": "Jack Daniels Whiskey", "amount": 85000.00 },
      { "sku": "Chicken Breast", "amount": 68000.00 },
      { "sku": "Frozen Prawns", "amount": 55000.00 },
      { "sku": "Cheddar Cheese", "amount": 48000.00 },
      { "sku": "Olive Oil", "amount": 43000.00 },
      { "sku": "Basmati Rice", "amount": 41000.00 },
      { "sku": "Red Wine Bottle", "amount": 35000.00 },
      { "sku": "Butter Blocks", "amount": 32000.00 },
      { "sku": "Pepsi 750ml", "amount": 29000.00 },
      { "sku": "Egg Tray", "amount": 21000.00 }
    ],

    "categoryPortfolio": [
      {
        "category": "Meat & Poultry",
        "count": 64,
        "amount": 305000.00,
        "countPer": 19.7,
        "amountPer": 32.3
      },
      {
        "category": "Seafood",
        "count": 34,
        "amount": 118000.00,
        "countPer": 10.5,
        "amountPer": 12.5
      },
      {
        "category": "Dairy",
        "count": 49,
        "amount": 132000.00,
        "countPer": 15.1,
        "amountPer": 14.0
      },
      {
        "category": "Grocery",
        "count": 145,
        "amount": 250000.00,
        "countPer": 44.8,
        "amountPer": 26.4
      },
      {
        "category": "Beverages",
        "count": 32,
        "amount": 140500.00,
        "countPer": 9.9,
        "amountPer": 14.8
      }
    ]
  };

};
