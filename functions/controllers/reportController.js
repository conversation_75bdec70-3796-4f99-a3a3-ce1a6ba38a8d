const admin = require("firebase-admin");
const { COLLECTIONS } = require("@/defs/collectionDefs");

// Firestore collections
const storeDb = admin.firestore().collection(COLLECTIONS.LOCATIONS);
const locationDb = admin.firestore().collection(COLLECTIONS.WORK_AREAS);

const categoryDb = admin.firestore().collection(COLLECTIONS.CATEGORIES);


const {reportType, reportColumns} = require("@/utils/reportFilter")

// Endpoint to get stores and locations by tenantId
exports.filterFunction = async (req, res) => {
  const { tenantId } = req.params;

  try {
    // Fetch stores using nested field
    const storeSnap = await storeDb.where("tenant.id", "==", tenantId).get();
    const stores = storeSnap.docs.map((doc) => ({ id: doc.id, ...doc.data() }));

    // Fetch locations using nested field
    const locationSnap = await locationDb.where("tenantId", "==", tenantId).get();
    const locations = locationSnap.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    // Fetch categories with subcategories using tenantId
    const categorySnap = await categoryDb.where("tenantId", "==", tenantId).get();
    const categories = categorySnap.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        name: data.name,
        subCategories: (data.subCategories || []).map((sub) => ({
          id: sub.id,
          name: sub.name,
        })),
      };
    });

    // Send full response
    return res.json({
      message: "success",
      stores,
      locations,
      categories,
      reportType
    });
  } catch (error) {
    console.error("Error fetching filter data:", error);
    return res
      .status(500)
      .json({ message: "Internal server error", error: error.message });
  }
};


exports.getReportConfig = async (req, res) => {
  const { reportTypeId } = req.params;

  try {
    const typeId = parseInt(reportTypeId, 10);

    const report = reportType.find((r) => r.id === typeId);
    const columns = reportColumns.find((c) => c.reportType === typeId);

    if (!report) {
      return res.status(404).json({ message: "Report type not found" });
    }

    return res.json({
      message: "success",
      reportType: report,
      reportColumns: columns ? columns.options : [],
    });
  } catch (error) {
    console.error("Error fetching report config:", error);
    return res.status(500).json({
      message: "Internal server error",
      error: error.message,
    });
  }
};
