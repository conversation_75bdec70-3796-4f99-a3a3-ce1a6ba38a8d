const admin = require("firebase-admin");
const db = admin.firestore();

exports.deleteByFilter = async (req, res) => {
  try {
    const { collectionName, field, value } = req.body;

    if (
      !Array.isArray(collectionName) ||
      collectionName.length === 0 ||
      !field ||
      value === undefined
    ) {
      return res.status(400).json({
        error:
          "Missing or invalid parameters: collectionName (array), field, value",
      });
    }

    let totalDeleted = 0;

    // Loop through each collection in the array
    for (const name of collectionName) {
      let deletedInCollection = 0;
      let batchCount = 0;

      while (true) {
        const snapshot = await db
          .collection(name)
          .where(field, "==", value)
          .limit(500)
          .get();

        if (snapshot.empty) break;

        const batch = db.batch();
        snapshot.docs.forEach((doc) => batch.delete(doc.ref));

        await batch.commit();

        deletedInCollection += snapshot.size;
        totalDeleted += snapshot.size;
        batchCount++;

        console.log(
          `Collection '${name}' → Batch ${batchCount}: deleted ${snapshot.size} docs`
        );
      }

      console.log(
        `Collection '${name}' completed. Total deleted: ${deletedInCollection}`
      );
    }

    return res.json({
      message: `Deleted ${totalDeleted} documents from ${collectionName.length} collection(s) where ${field} = ${value}`,
    });
  } catch (error) {
    console.error("Error deleting documents:", error);
    return res.status(500).json({ error: error.message });
  }
};
