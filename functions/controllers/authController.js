const axios = require("axios");
const admin = require("firebase-admin");
const jwt = require("jsonwebtoken");
const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");
const { COLLECTIONS } = require("@/defs/collectionDefs");
/**
 * @description
 * Silent login API. This API is used to
 * - Validate the token
 * - Fetch the user
 * - Fetch the tenants associated with the user
 * - Return the user details and a JWT token
 * @param {Object} req - Request object
 * @param {string} req.body.token - The token to be validated
 * @returns {Object} - An object with the user details and a JWT token
 * @throws {Error} - If the token is invalid
 */
exports.silentLogin = async (req, res) => {
  const { oAuthCode } = req.body;

  try {
    // 1. Exchange OAuth code with your Auth server
    const { data } = await axios.post(
      `${process.env.AUTH_SERVER_URL}/sso/token-exchange`,
      { oAuthToken: oAuthCode }
    );

    const { userEmail, userId } = data;

    // 2. Find user in Firestore
    const usersRef = admin.firestore().collection(COLLECTIONS.USERS);
    const usersSnapshot = await usersRef
      .where("digitorySsoId", "==", userId)
      .get();

    if (usersSnapshot.empty) {
      return res
        .status(403)
        .json({ message: "User does not have access to the app" });
    }
    // 3. Build tenants array (use for...of for async queries)
    const tenants = [];

    for (const doc of usersSnapshot.docs) {
      const user = doc.data();
      if (user.emailId === userEmail) {
        let privileges = [];

        // If Admin, fetch all privileges
        if (user.roleName?.toLowerCase() === "admin") {
          const allRolesSnap = await admin.firestore().collection(COLLECTIONS.ROLE).get();
          privileges = allRolesSnap.docs.flatMap(
            (doc) => doc.data().privileges || []
          );
          privileges = [...new Set(privileges)]; // remove duplicates
        } else {
          // Fetch role-specific privileges
          const roleQuery = await admin
            .firestore()
            .collection(COLLECTIONS.ROLE)
            .where("name", "==", user.roleName)
            .where("tenantId", "==", user.tenantId)
            .limit(1)
            .get();

          if (roleQuery.empty) continue;

          const roleData = roleQuery.docs[0].data();
          privileges = roleData.privileges || [];
        }

        tenants.push({
          id: user.tenantId,
          name: user.tenantName,
          roleName: user.roleName,
          roleId: user.roleId,
          userName: user.name || userEmail.split("@")[0],
          privileges,
          isAdmin: user.roleName?.toLowerCase() === "admin",
          userId: user.id,
          userDetails: user
        });
      }
    }

    if (tenants.length === 0) {
      return res
        .status(403)
        .json({ message: "No valid tenants found for this user" });
    }

    // 4. Create your own JWT
    const claims = {
      userId,
      email: userEmail,
      tenants,
      claimsIssuedAt: FD.now()
    };

    const jwtToken = jwt.sign(
      claims,
      process.env.JWT_SECRET, // 👈 keep secret in env
      { expiresIn: "6h" } // token valid for 6 hour
    );

    // 6. Return response
    res.status(200).json({
      userId,
      tenants,
      email: userEmail,
      token: jwtToken, // ID token lifetime ~6 hour
      expiresIn: 3600 *6,
    });
  } catch (error) {
    console.error(error, "errorMessage");
    res.status(401).json({ message: "Invalid token" });
  }
};
