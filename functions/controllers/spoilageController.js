const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");
const {
  createSpoilage,
  getSpoilages,
  getSpoilageById,
  updateSpoilage,
} = require("@/services/spoilageService");

exports.insertSpoilage = async (req, res) => {
  const payload = {
    ...req.body,
    tenantId: req.params.tenantId,
    requestedBy: {
      id: req.identity.userId,
      name: req.identity.userName,
      time: FD.now(),
    },
  };
  try {
    const spoilage = await createSpoilage(payload);
    res.status(201).json(spoilage);
  } catch (error) {
    console.error("Error creating spoilage:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.getSpoilages = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
     const filters = {
      tenantId: tenantId,
      locations: req.body.locations || null,
      inventoryLocations: req.body.inventoryLocations || null,
      fromDate: req.body.fromDate || null,
      toDate: req.body.toDate || null,
    };
    const spoilages = await getSpoilages(filters);
    res.status(200).json(spoilages);
  } catch (error) {
    console.error("Error fetching spoilages:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.getSpoilageById = async (req, res) => {
  try {
    const spoilage = await getSpoilageById(req.params.id);
    res.status(200).json(spoilage);
  } catch (error) {
    console.error("Error fetching spoilage:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.updateSpoilage = async (req, res) => {
  const payload = {
    ...req.body,
    completedBy: {
      id: req.identity.userId,
      name: req.identity.userName,
      time: FD.now(),
    },
  };
  try {
    await updateSpoilage(req.params.id, payload);
    res.status(200).json({ message: "updated successfully" });
  } catch (err) {
    console.error("Error updating spoilage:", err);
    res.status(500).json({ message: err.message });
  }
};
