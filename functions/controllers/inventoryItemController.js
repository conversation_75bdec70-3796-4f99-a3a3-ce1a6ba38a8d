const admin = require("firebase-admin");
const schema = require("@/models/inventoryItemSchema");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const db = admin.firestore().collection(COLLECTIONS.INVENTORY_ITEMS);
const { rupeeToPaise, paiseToRupee } = require("@/utils/money");
const { getActiveContracts } = require("@/repositories/contractRepo");
const {
  handleValidation,
  trimName,
  nameValidation,
} = require("@/utils/validation");
const {
  getNextInventoryItemId,
  getNextPackageId,
} = require("@/services/counterService");
const itemService = require("@/services/itemService");
const { findCurrentContractItemPrice } = require("@/services/contractService");
const { getInventoryItemStocks } = require("@/repositories/stockRepo");
const {
  getDefaultWorkAreaByLocationId,
} = require("@/repositories/locationRepo");
const { propagateInventoryItemName } = require("@/utils/updateLinkedData");
const masterService = require("@/services/masterService");
const { getTransferDataById } = require("@/repositories/transferRepo");
const { getVendors } = require("@/repositories/masterRepo");

const isUniquePackages = (validatedData, res) => {
  const nameSet = new Set();
  const codeSet = new Set();
  const duplicateNames = [];
  const duplicateCodes = [];

  for (const p of validatedData.packages) {
    // check names
    if (nameSet.has(p.name)) {
      duplicateNames.push(p.name);
    } else {
      nameSet.add(p.name);
    }

    // check codes
    if (codeSet.has(p.packageCode)) {
      duplicateCodes.push(p.packageCode);
    } else {
      codeSet.add(p.packageCode);
    }
  }

  if (duplicateNames.length > 0) {
    res.status(400).json({
      message: `Duplicate Package Names: ${duplicateNames.join(", ")}`,
    });
    return false;
  }

  if (duplicateCodes.length > 0) {
    res.status(400).json({
      message: `Duplicate Package Codes: ${duplicateCodes.join(", ")}`,
    });
    return false;
  }
  return true;
};

exports.insertInventoryItem = async (req, res) => {
  const tenantId = req.body.tenantId;
  let invItemCode = req.body.itemCode?.trim();
  let hsnCode = req.body.hsnCode?.trim();

  try {
    if (!invItemCode) {
      invItemCode = await getNextInventoryItemId(tenantId);
    }
    const validatedData = handleValidation(
      { ...req.body, itemCode: invItemCode },
      schema
    );
    if (!validatedData) return;
    // check uniqueness ItemCode
    const duplicateInvId = await db
      .where("tenantId", "==", tenantId)
      .where("itemCode", "==", invItemCode)
      .limit(1)
      .get();

    if (!duplicateInvId.empty) {
      return res
        .status(400)
        .json({ message: "Inventory Item Code Already Exists" });
    }

    if (hsnCode) {
      const duplicateHSN = await db
        .where("tenantId", "==", tenantId)
        .where("hsnCode", "==", hsnCode)
        .limit(1)
        .get();

      if (!duplicateHSN.empty) {
        return res.status(400).json({ message: "HSN Code Already Exists" });
      }
    }

    // check uniqueness name
    const { valid, normalizedName, error } = await nameValidation(
      validatedData.itemName,
      db,
      null,
      "tenantId",
      validatedData.tenantId
    );
    if (!valid) return res.status(400).json({ message: error });

    const cleanName = trimName(validatedData.itemName);

    validatedData.packages = await Promise.all(
      validatedData.packages.map(async (pkg) => {
        let packageCode = pkg.packageCode?.trim();
        if (!packageCode) {
          packageCode = await getNextPackageId(tenantId);
        }
        return {
          ...pkg,
          id: db.doc().id,
          packageCode,
          unitCost: rupeeToPaise(pkg.unitCost),
        };
      })
    );

    const isUnique = isUniquePackages(validatedData, res);
    if (!isUnique) return;

    validatedData.ingredients = validatedData.ingredients.map((ing) => ({
      ...ing,
      unitCost: rupeeToPaise(ing.unitCost),
    }));
    validatedData.unitCost = rupeeToPaise(validatedData.unitCost);

    const newDocRef = db.doc();
    const inventoryItem = {
      ...validatedData,
      id: newDocRef.id,
      itemName: cleanName,
      nameNormalized: normalizedName,
    };
    await newDocRef.set(inventoryItem);
    res.status(201).json(inventoryItem);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.getInventoryItem = async (id) => {
  const inventoryItem = await db.doc(id).get();
  if (!inventoryItem.exists) return null;
  const data = inventoryItem.data();
  data.packages = data.packages.map((pkg) => ({
    ...pkg,
    unitCost: paiseToRupee(pkg.unitCost),
  }));
  data.ingredients = data.ingredients.map((ing) => ({
    ...ing,
    unitCost: paiseToRupee(ing.unitCost),
  }));
  return { ...data, unitCost: paiseToRupee(data.unitCost) };
};

exports.getInventoryItemById = async (req, res) => {
  try {
    const inventoryItem = await this.getInventoryItem(req.params.id);
    if (!inventoryItem)
      return res.status(404).json({ message: "inventory Item Not Found" });
    return res.status(200).json(inventoryItem);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.getInventoryItems = async (req, res) => {
  try {
    const { tenantId } = req.params;

    if (!tenantId) {
      return res.status(400).json({
        status: "error",
        message: "tenantId is required",
      });
    }

    // Query Firestore for items by tenantId
    const snapshot = await db
      .where("tenantId", "==", tenantId)
      .orderBy("nameNormalized")
      .get();

    if (snapshot.empty) {
      return res.status(200).json([]);
    }

    const response = snapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        ...data,
        unitCost: paiseToRupee(data.unitCost),
        packages: (data.packages || []).map((pkg) => ({
          ...pkg,
          unitCost: paiseToRupee(pkg.unitCost),
        })),
        ingredients: (data.ingredients || []).map((ing) => ({
          ...ing,
          unitCost: paiseToRupee(ing.unitCost),
        })),
      };
    });

    res.status(200).json(response);
  } catch (error) {
    console.error("Error fetching inventory:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.updateInventoryItem = async (req, res) => {
  const tenantId = req.body.tenantId;
  let invItemCode = req.body.itemCode?.trim();
  let hsnCode = req.body.hsnCode?.trim();

  try {
    if (!invItemCode) {
      invItemCode = await getNextInventoryItemId(tenantId);
    }
    const validatedData = handleValidation(
      { ...req.body, itemCode: invItemCode },
      schema
    );
    if (!validatedData) return;
    // check uniqueness ItemCode
    const duplicateInvId = await db
      .where("tenantId", "==", tenantId)
      .where("itemCode", "==", invItemCode)
      .limit(1)
      .get();

    if (!duplicateInvId.empty && duplicateInvId.docs[0].id !== req.params.id) {
      return res
        .status(400)
        .json({ message: "Inventory Item Code Already Exists" });
    }

    // check uniqueness for HSN Code (ignore current item)
    if (hsnCode) {
      const duplicateHSN = await db
        .where("tenantId", "==", tenantId)
        .where("hsnCode", "==", hsnCode)
        .limit(1)
        .get();

      if (
        !duplicateHSN.empty &&
        duplicateHSN.docs[0].id !== req.params.id // ✅ skip current item
      ) {
        return res.status(400).json({ message: "HSN Code Already Exists" });
      }
    }

    // check uniqueness name
    const { valid, normalizedName, error } = await nameValidation(
      validatedData.itemName,
      db,
      req.params.id,
      "tenantId",
      validatedData.tenantId
    );
    if (!valid) return res.status(400).json({ message: error });

    const cleanName = trimName(validatedData.itemName);

    const inventoryItem = db.doc(req.params.id);

    validatedData.packages = await Promise.all(
      validatedData.packages.map(async (pkg) => {
        let packageCode = pkg.packageCode?.trim();
        if (!packageCode) {
          packageCode = await getNextPackageId(tenantId);
          console.log(packageCode, "update");
        }
        return {
          ...pkg,
          id: pkg.id || db.doc().id,
          packageCode,
          unitCost: rupeeToPaise(pkg.unitCost),
        };
      })
    );

    const isUniquePkg = isUniquePackages(validatedData, res);
    if (!isUniquePkg) return;

    validatedData.ingredients = validatedData.ingredients.map((ing) => ({
      ...ing,
      unitCost: rupeeToPaise(ing.unitCost),
    }));
    validatedData.unitCost = rupeeToPaise(validatedData.unitCost);

    await inventoryItem.update({
      ...validatedData,
      itemName: cleanName,
      nameNormalized: normalizedName,
    });

    await propagateInventoryItemName(req.params.id, cleanName);

    res.status(200).json("Updated Successfully");
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Controller for activating a item
exports.activateItem = async (req, res) => {
  try {
    const { tenantId, id } = req.params; // Get item ID from URL
    const result = await itemService.activateItem(tenantId, id); // Call service to activate

    // Send success response
    res.status(200).json({
      message: "Item activated successfully",
    });
  } catch (err) {
    // Handle errors (e.g., item not found)
    res.status(400).json({ message: err.message });
  }
};

// Controller for deactivating a item
exports.deactivateItem = async (req, res) => {
  try {
    const { tenantId, id } = req.params; // Get item ID from URL
    const result = await itemService.deactivateItem(tenantId, id); // Call service to deactivate (with validation)

    // Send success response
    res.status(200).json({
      message: "Item deactivated successfully",
    });
  } catch (err) {
    // Handle first validation error (e.g., linked menu items/orders)
    res.status(400).json({ message: err.message });
  }
};

exports.getInventoryItemDetails = async (req, res) => {
  try {
    const { tenantId, id } = req.params;
    const { locationId, vendorId, pkgId, inventoryLocationId } = req.body;
    const { contract = "true" } = req.query; // default is "true"

    const includeContract = contract === "true";

    // ✅ Basic validation
    if (!tenantId || !id || !locationId || !inventoryLocationId) {
      return res.status(400).json({ message: "Missing required parameters" });
    }

    // If contract is true, vendorId is mandatory
    if (includeContract && !vendorId) {
      return res.status(400).json({ message: "vendorId is required" });
    }

    // ✅ Step 1: Fetch inventory item
    const inventoryItem = await this.getInventoryItem(id);

    if (!inventoryItem) {
      return res.status(404).json({ message: "Inventory item not found" });
    }

    if (!inventoryLocationId) {
      return res
        .status(404)
        .json({ message: "Work area not found for this location" });
    }

    // ✅ Step 2: Conditionally fetch contract price
    let contractPrice = null;
    let contractType = null;
    let inclTax = null;
    let contractId = null;
    let contractNumber = null;

    if (includeContract) {
      const contractData = await findCurrentContractItemPrice({
        tenantId,
        itemId: id,
        locationId,
        vendorId,
        pkgId,
      });

      contractPrice = contractData.contractPrice;
      contractType = contractData.contractType;
      inclTax = contractData.inclTax;
      contractId = contractData.contractId;
      contractNumber = contractData.contractNumber;
    }

    // ✅ Step 3: Fetch stock for this work area
    const [stockResult] = await getInventoryItemStocks(inventoryLocationId, [
      { itemId: id, pkgId },
    ]);
    const inStock = stockResult?.inStock ?? 0;

    // ✅ Step 4: Return final response

    const result = { ...inventoryItem, inStock };

    if (includeContract) {
      result.contractPrice = contractPrice;
      result.contractType = contractType;
      result.inclTax = inclTax;
      result.contractId = contractId;
      result.contractNumber = contractNumber;
    }
    return res.status(200).json(result);
  } catch (error) {
    console.error("Error fetching inventory item details:", error);
    return res.status(500).json({ message: error.message });
  }
};

exports.getTransferDetails = async (req, res) => {
  try {
    const { tenantId, id } = req.params;

    const result = await masterService.getInventoryMasters(tenantId);
    const transferDetails = await getTransferDataById(id, tenantId);
    const locationId = transferDetails.requester.locationId;

    let requiredItems = result
    .filter(r =>
      transferDetails.items.some(t => t.itemId === r.id)
    )
    .map(r => {
      // find matching item in second array
      const match = transferDetails.items.find(t => t.itemId === r.id);
  
      return {
        ...r,
        requestedPkg: match?.pkg?.name || null,
        requestedPkgId: match?.pkg?.id || null
      };
    });
    const fetchActiveVendors = await getVendors(tenantId);


    requiredItems.forEach(item => {
      item.vendorDetails = item.vendors
        .map(vId => {
          const v = fetchActiveVendors.find(f => f.id === vId);
          if (!v) return null;
    
          return {
            vendorId: v.id,
            vendorName: v.name
          };
        })
        .filter(Boolean); // remove nulls
    });
    

    const activeContracts = await getActiveContracts(tenantId); // restrict based on current date active contracts

    const requiredContracts = activeContracts.filter(item =>
      item.location.some(loc => loc.id === locationId)
    );
    
    requiredItems.forEach(item => {
      // Check if item exists in requiredContracts
      const contractItem = requiredContracts
        .flatMap(contract =>
          contract.items.map(contractItem => ({
            ...contractItem,
            contractVendorId: contract.vendor.id,
            contractType: contractItem.contractType,
            inclTax: contractItem.inclTax
          }))
        )
        .find(contractItem =>
          contractItem.itemId === item.id &&
          contractItem.pkg?.name === item.requestedPkg
        );

      if (contractItem) {
        // Find the matching vendor in vendorDetails
        const matchingVendorIndex = item.vendorDetails.findIndex(
          vendor => vendor.vendorId === contractItem.contractVendorId
        );

        if (matchingVendorIndex !== -1) {
          // Create contract-based vendor entry
          const contractVendor = {
            vendorId: contractItem.contractVendorId,
            vendorName: item.vendorDetails[matchingVendorIndex].vendorName,
            price: paiseToRupee(contractItem.contractPrice),
            taxRate: 0,
            contractType: contractItem.contractType,
            inclTax: contractItem.inclTax
          };

          // Remove the existing vendor entry
          item.vendorDetails.splice(matchingVendorIndex, 1);

          // Insert contract-based vendor as 0th element
          item.vendorDetails.unshift(contractVendor);
        }
      }
    })
    let obj = {
      "items": requiredItems,
      "contracts": requiredContracts
    }


    res.status(200).json(obj);
  } catch (error) {
    console.error("Error fetching transfer details:", error);
    res.status(500).json({ message: error.message });
  }
};
