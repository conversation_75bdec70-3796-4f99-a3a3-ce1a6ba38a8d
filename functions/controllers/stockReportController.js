// controllers/stockReportController.js

const { aggregateStocks, getLedgers } = require("@/services/stockService");
const { ResultTypes } = require("@/defs/resultTypeDefs");

/**
 * Get aggregated stocks
 * @param {Object} req.body - Must contain tenantId, and optionally locationId, inventoryLocationId, categoryId, subCategoryId, itemId
 * @param {string} req.body.aggregatorType - "item" | "item_loc" | "item_inv_loc"
 * @param {string} req.body.outputType - "json" | "excel" | "pdf"
 * @returns {Promise<Object>} - Promise that resolves to a JSON object containing the aggregated stocks
 */
exports.getStocks = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
    if (!tenantId) {
      return res.status(400).json({
        success: false,
        message: "tenantId is required",
      });
    }

    const { filters } = req.body;

    const aggregatorType = req.body.aggregatorType || "";
    const result = await aggregateStocks(tenantId, filters, aggregatorType);

    switch (req.body.outputType) {
      case ResultTypes.EXCEL:
        return;
      default:
        return res.status(200).json(result);
    }
  } catch (error) {
    console.error("Error fetching stocks:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};

exports.getStockLedgers = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
    if (!tenantId) {
      return res.status(400).json({
        success: false,
        message: "tenantId is required",
      });
    }

    const filters = {
      locationId: req.body.locationId || null,
      inventoryLocationId: req.body.inventoryLocationId || null,
      categoryId: req.body.categoryId || null,
      subCategoryId: req.body.subCategoryId || null,
      itemId: req.body.itemId || null,
      ledgerType: req.body.ledgerType || null,
    };

    // const aggregatorType = req.body.aggregatorType || "";
    const result = await getLedgers(tenantId, filters);

    switch (req.body.outputType) {
      case ResultTypes.EXCEL:
        return;
      default:
        return res.status(200).json(result);
    }
  } catch (error) {
    console.error("Error fetching stocks:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};
