/**
 * Stock Movement Reports Controller
 * ------------------------------
 * Handles all tenant-specific stock movement report requests.
 */

const { REPORTS } = require("@/defs/reportDefs");
const { createReportHandler } = require("@/helpers/reportHandlerHelper");
const stockMovementService = require("@/services/stockMovementReportService");


// Transfer Report
exports.getTransferListReport = createReportHandler(REPORTS.TRANSFER_LIST, stockMovementService.getTransferListReport);

// Dispatch Transfer Report
exports.getDispatchTransferReport = createReportHandler(REPORTS.DISPATCH_TRANSFER, stockMovementService.getDispatchTransferReport);

// Detailed Transfer Report
exports.getDetailedTransferReport = createReportHandler(REPORTS.DETAILED_TRANSFER, stockMovementService.getDetailedTransferReport);
