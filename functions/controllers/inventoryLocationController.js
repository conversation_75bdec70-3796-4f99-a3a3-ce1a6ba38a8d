const admin = require("firebase-admin");
const schema = require("@/models/inventoryLocationSchema");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const db = admin.firestore().collection(COLLECTIONS.WORK_AREAS);
const storeDb = admin.firestore().collection(COLLECTIONS.LOCATIONS);
const {
  handleValidation,
  trimName,
  nameValidation,
} = require("@/utils/validation");
const locationService = require("@/services/locationService");
const { propagateWorkareaName } = require("@/utils/updateLinkedData");

exports.insertInventoryLocation = async (req, res) => {
  try {
    const validatedData = handleValidation(req.body, schema);
    if (!validatedData) return;
    // check uniqueness name
    const { valid, normalizedName, error } = await nameValidation(
      validatedData.name,
      db,
      null,
      "locationId",
      validatedData.locationId
    );
    if (!valid) return res.status(400).json({ message: error });

    const cleanName = trimName(validatedData.name);
    const newDocRef = db.doc();
    const inventoryLocation = {
      ...validatedData,
      id: newDocRef.id,
      name: cleanName,
      nameNormalized: normalizedName,
    };
    await newDocRef.set(inventoryLocation);
    res.status(200).json(inventoryLocation);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.getInventoryLocations = async (req, res) => {
  try {
    const { tenantId } = req.params;
    if (!tenantId) {
      return res.status(400).json({
        status: "error",
        message: "tenantId is required",
      });
    }
    const snapshot = await db
      .where("tenantId", "==", tenantId)
      .orderBy("nameNormalized")
      .get();

    const locations = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));
    res.status(200).json(locations);
  } catch (error) {
    console.error("Error Fetching Inventory Work/Storage Area:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.getInventoryLocationById = async (req, res) => {
  try {
    const inventoryLocation = await db.doc(req.params.id).get();
    if (!inventoryLocation.exists)
      return res
        .status(404)
        .json({ message: "Inventory Work/Storage Area Not Found" });
    return res.status(200).json(inventoryLocation.data());
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.updateInventoryLocation = async (req, res) => {
  try {
    const validatedData = handleValidation(req.body, schema);
    if (!validatedData) return;
    // check uniqueness name
    const { valid, normalizedName, error } = await nameValidation(
      validatedData.name,
      db,
      req.params.id,
      "locationId",
      validatedData.locationId
    );
    if (!valid) return res.status(400).json({ message: error });

    const cleanName = trimName(validatedData.name);

    const locationRef = db.doc(req.params.id);
    await locationRef.update({
      ...validatedData,
      name: cleanName,
      nameNormalized: normalizedName,
    });

    await propagateWorkareaName(req.params.id, cleanName);

    res.status(200).json("Updated Successfully");
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.updateLocationActiveStatus = async (req, res) => {
  try {
    const locationDoc = db.doc(req.params.id);
    const location = await locationDoc.get();
    const activeStatus = !location.data().activeStatus;
    await locationDoc.update({ activeStatus });
    res.status(200).json("Status Updated Successfully");
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.getStoreLocations = async (req, res) => {
  const locationId = req.params?.storeId?.trim();
  if (!locationId) {
    return res.status(400).json({ message: "Field locationId is Required." });
  }

  try {
    const data = await db
      .where("locationId", "==", locationId)
      .orderBy("nameNormalized")
      .get();
    const locations = data.docs.map((doc) => doc.data());
    return res.status(200).json(locations);
  } catch (error) {
    return res.status(500).json({ message: error.message });
  }
};


// Controller for activating a location
exports.activateLocation = async (req, res) => {
  try {
    const { tenantId, id } = req.params; // Get location ID from URL
    const result = await locationService.activateLocation(tenantId, id); // Call service to activate

    // Send success response
    res.status(200).json({
      message: "Location activated successfully",
    });
  } catch (err) {
    // Handle errors (e.g., location not found)
    res.status(400).json({ message: err.message });
  }
};

// Controller for deactivating a location
exports.deactivateLocation = async (req, res) => {
  try {
    const { tenantId, id } = req.params; // Get location ID from URL
    const result = await locationService.deactivateLocation(tenantId, id); // Call service to deactivate (with validation)

    // Send success response
    res.status(200).json({
      message: "Location deactivated successfully",
    });
  } catch (err) {
    // Handle first validation error (e.g., linked menu items/orders)
    res.status(400).json({ message: err.message });
  }
};