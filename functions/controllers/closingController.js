const {
  getClosingItemsRequest,
  createClosingRequest,
  getClosingDataRequest,
  getClosingById,
} = require("@/services/closingService");

const { FirestoreDateHelper: FD } = require("@/helpers/dateHelper");

exports.getClosingItems = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
    const locationId = req.body.locationId;
    const result = await getClosingItemsRequest(tenantId, locationId);
    return res.status(200).json(result);
  } catch (error) {
    console.error("Error fetching closing items:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};

exports.getClosingById = async (req, res) => {
  try {
    const { id } = req.params;
    const closing = await getClosingById(id);
    if (!closing) {
      return res.status(404).json({ message: "closing data not found" });
    }
    res.status(200).json(closing);
  } catch (error) {
    console.error("Error fetching closing data:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.getClosingData = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
    const filters = {
      tenantId: tenantId,
      locations: req.body.locations || null,
      inventoryLocations: req.body.inventoryLocations || null,
      fromDate: req.body.fromDate || null,
      toDate: req.body.toDate || null,
      stockCorrection: req.body.stockCorrection || null,
    };
    const result = await getClosingDataRequest(filters);
    return res.status(200).json(result);
  } catch (error) {
    console.error("Error fetching closing data:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};

exports.createClosing = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
    if (!tenantId) {
      return res
        .status(400)
        .json({ success: false, message: "tenantId is required" });
    }

    const { userName, userId } = req.identity;

    const data = {
      ...req.body,
      tenantId,
      closedBy: {
        userId,
        userName,
        time: FD.now(),
      },
    };
    const closingRequest = await createClosingRequest(data);
    res.status(200).json(closingRequest);
  } catch (error) {
    console.error("error creating indent request", error);
    res.status(500).json({ message: error.message });
  }
};
