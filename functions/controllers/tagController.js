const admin = require("firebase-admin");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const db = admin.firestore().collection(COLLECTIONS.TAGS);
const schema = require("@/models/tagSchema");

const tagService = require("@/services/tagService");

exports.createTag = async (req, res) => {
    const { error, value } = schema.validate(req.body);

    if (error) {
        return res.status(400).json({ status: "error", message: error.message });
    }

    const { tenantId, name } = req.body;

    if (!tenantId) {
        return res.status(400).json({
            status: "error",
            message: "tenantId is required",
        });
    }

    try {
        const existingTag = await db
            .where("tenantId", "==", tenantId)
            .where("name", "==", name)
            .limit(1)
            .get();

        if (!existingTag.empty) {
            return res.status(400).json({
                status: "error",
                message: `Tag '${name}' already exists`,
            });
        }

        const newTagRef = db.doc();
        const newTag = {
            id: newTagRef.id,
            ...value,
        };

        await newTagRef.set(newTag);

        res.status(201).json({
            status: "success",
            message: "Tag created successfully",
            payload: newTag,
        });
    } catch (err) {
        res.status(500).json({ status: "error", message: err.message });
    }
};

exports.getTags = async (req, res) => {
    const { tenantId } = req.params;

    if (!tenantId) {
        return res.status(400).json({
            status: "error",
            message: "tenantId is required",
        });
    }

    try {
        const snapshot = await db.where("tenantId", "==", tenantId).get();
        const tags = snapshot.docs.map((doc) => {
            const data = doc.data();
            return data;
        });

        res.json({
            status: "success",
            message: "Tags fetched successfully",
            payload: tags,
        });
    } catch (err) {
        res.status(500).json({ status: "error", message: err.message });
    }
};

exports.getTagById = async (req, res) => {
    try {
        const tagRef = db.doc(req.params.id);
        const doc = await tagRef.get();

        if (!doc.exists) {
            return res.status(404).json({
                status: "error",
                message: "Tag not found",
            });
        }

        res.json({
            status: "success",
            message: "Tag fetched successfully",
            payload: doc.data(),
        });
    } catch (err) {
        res.status(500).json({
            status: "error",
            message: err.message,
        });
    }
};

exports.updateTag = async (req, res) => {
    const { error, value } = schema.validate(req.body);
    if (error) {
        return res.status(400).json({ status: "error", message: error.message });
    }

    try {
        const tagRef = db.doc(req.params.id);
        const doc = await tagRef.get();
        if (!doc.exists) {
            return res
                .status(404)
                .json({ status: "error", message: "Tag not found" });
        }

        const { tenantId, name } = value;

        const existingTag = await db
            .where("tenantId", "==", tenantId)
            .where("name", "==", name)
            .limit(1)
            .get();

        if (!existingTag.empty && existingTag.docs[0].id !== req.params.id) {
            return res.status(400).json({
                status: "error",
                message: `Tag '${name}' already exists`,
            });
        }

        await tagRef.update(value);

        res.json({
            status: "success",
            message: "Tag updated successfully",
            payload: { id: req.params.id, ...value },
        });
    } catch (err) {
        res.status(500).json({ status: "error", message: err.message });
    }
};

// Controller for activating a tag
exports.activateTag = async (req, res) => {
  try {
    const { tenantId, id } = req.params; // Get tag ID from URL
    const result = await tagService.activateTag(tenantId, id); // Call service to activate

    // Send success response
    res.status(200).json({
      message: "Tag activated successfully",
    });
  } catch (err) {
    // Handle errors (e.g., tag not found)
    res.status(400).json({ message: err.message });
  }
};

// Controller for deactivating a tag
exports.deactivateTag = async (req, res) => {
  try {
    const { tenantId, id } = req.params; // Get tag ID from URL
    const result = await tagService.deactivateTag(tenantId, id); // Call service to deactivate (with validation)

    // Send success response
    res.status(200).json({
      message: "Tag deactivated successfully",
    });
  } catch (err) {
    // Handle first validation error (e.g., linked menu items/orders)
    res.status(400).json({ message: err.message });
  }
};