const admin = require("firebase-admin");
const schema = require("@/models/menuItemSchema");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const db = admin.firestore().collection(COLLECTIONS.MENU_ITEMS);
const { handleValidation } = require("@/utils/validation");

exports.getMenuItems = async (req, res) => {
  const tenantId = req.params?.tenantId?.trim();
  if (!tenantId) {
    return res.status(400).json({ message: "Field tenantId is required." });
  }

  try {
    const data = await db.where("tenantId", "==", tenantId).get();
    const menuItems = data.docs.map((doc) => doc.data());
    return res.status(200).json(menuItems);
  } catch (error) {
    return res.status(500).json({ message: "Internal server error." });
  }
};

exports.getMenuItemById = async (req, res) => {
  const id = req.params?.id?.trim();
  const tenantId = req.params?.tenantId?.trim();
  const accountId = req.query?.accountId?.trim();

  if (!id || !tenantId || !accountId) {
    return res
      .status(400)
      .json({ message: "id, tenantId, and accountId are required." });
  }

  try {
    const snapshot = await db
      .where("tenantId", "==", tenantId)
      .where("account.id", "==", accountId)
      .where("id", "==", id)
      .get();

    if (snapshot.empty) {
      return res.status(404).json({ message: "Menu Item not found." });
    }
    const result = [];
    snapshot.forEach((doc) => {
      result.push({ id: doc.id, ...doc.data() });
    });
    return res.status(200).json(result[0]);
  } catch (error) {
    console.error("Error fetching menu item by ID:", error);
    return res.status(500).json({ message: "Internal server error." });
  }
};

exports.updateMenuItem = async (req, res) => {
  const id = req.params?.id?.trim();
  const tenantId = req.body?.tenantId?.trim();
  const accountId = req.query?.accountId?.trim();
  const updatedData = req.body;

  if (!id || !tenantId || !accountId) {
    return res
      .status(400)
      .json({ message: "id, tenantId, and accountId are required." });
  }

  try {
    const validatedData = handleValidation(updatedData, schema);
    if (!validatedData) return;
    const querySnapshot = await db
      .where("tenantId", "==", tenantId)
      .where("account.id", "==", accountId)
      .where("id", "==", id)
      .limit(1)
      .get();

    if (querySnapshot.empty) {
      return res.status(404).json({ message: "Menu Item not found." });
    }

    const doc = querySnapshot.docs[0];
    const tenant = doc.data();

    if (tenant?.tenantId !== tenantId) {
      return res
        .status(403)
        .json({ message: "Unauthorized access to update this menu item." });
    }

    await doc.ref.update({ ...validatedData, linkingStatus: true });
    return res.status(200).json({ message: "Menu Item updated successfully." });
  } catch (error) {
    return res.status(500).json({ message: "Internal server error." });
  }
};

exports.insertMenuItems = async (req, res) => {
  try {
    const input = req.body;
    const menuItems = Array.isArray(input) ? input : [input];

    const accountDb = admin.firestore().collection(COLLECTIONS.ACCOUNT);

    const accSnapshot = await accountDb
      .where("posId", "==", menuItems[0].accountId)
      .get();
    const account = accSnapshot.docs[0].data();

    if (!account) {
      return res.status(404).json({ message: "Account not found." });
    }

    const preparedMenuItems = menuItems.map((menuItem) => ({
      posId: menuItem.posId,
      itemName: menuItem.itemName,
      itemCode: menuItem.itemCode,
      servingLevels: menuItem.servingLevels ?? [],
      account: {
        id: account.id,
        name: account.name,
        posId: account.posId,
      },
      tenantId: account.tenantId,
    }));

    const validMenuItems = await this.insertMenuItemLogic(
      preparedMenuItems,
      res
    );

    res.status(201).json({
      message: "MenuItems created/updated successfully",
      count: validMenuItems.length,
    });
  } catch (error) {
    res.status(500).send(error.message || "Internal Server Error");
  }
};

exports.insertMenuItemLogic = async (menuItems, res) => {
  const batch = admin.firestore().batch();
  const validMenuItems = [];

  for (const item of menuItems) {
    const validatedData = handleValidation(item, schema);
    if (!validatedData) {
      console.log("Invalid data, skipping.", item);
      continue;
    }

    const snapshot = await db
      .where("posId", "==", validatedData.posId)
      .where("account.id", "==", validatedData.account.id)
      .limit(1)
      .get();

    if (!snapshot.empty) {
      const existingDoc = snapshot.docs[0];
      const docRef = db.doc(existingDoc.id);
      batch.update(docRef, validatedData);
    } else {
      const docRef = db.doc();
      batch.set(docRef, { ...validatedData, id: docRef.id });
    }

    validMenuItems.push(validatedData);
  }

  await batch.commit();
  return validMenuItems;
};
