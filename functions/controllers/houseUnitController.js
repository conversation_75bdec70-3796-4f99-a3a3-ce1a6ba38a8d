const admin = require("firebase-admin");
const db = admin.firestore();
const schema = require("@/models/houseUnitSchema");
const { DEFAULT_UNITS } = require("@/utils/defaultData");
const {
  handleValidation,
  trimName,
  nameValidation,
} = require("@/utils/validation");
const { COLLECTIONS } = require("@/defs/collectionDefs");

const houseUnitCollection = admin
  .firestore()
  .collection(COLLECTIONS.HOUSE_UNITS);

const houseUnitService = require("@/services/houseUnitService");
const { propagateHouseUnitName } = require("@/utils/updateLinkedData");

// Helper function to check against DEFAULT_UNITS
const isDuplicateWithDefaultUnits = (name, symbol) => {
  return DEFAULT_UNITS.some(
    (unit) =>
      unit.name.toLowerCase() === name.toLowerCase() ||
      unit.symbol.toLowerCase() === symbol.toLowerCase()
  );
};

exports.insertHouseUnit = async (req, res) => {
  try {
    // Validate body using schema
    const validatedData = handleValidation(req.body, schema);
    if (!validatedData) return;

    const { tenantId, name, symbol } = validatedData;

    // Normalize name input early
    const cleanName = trimName(name);

    // Verify normalized name uniqueness within same tenant
    const { valid, normalizedName, error } = await nameValidation(
      cleanName,
      houseUnitCollection,
      null,
      "tenantId",
      tenantId
    );
    if (!valid) {
      return res.status(400).json({ message: error });
    }

    // Check against DEFAULT_UNITS
    if (isDuplicateWithDefaultUnits(cleanName, symbol)) {
      return res
        .status(400)
        .json({ message: "House unit name or symbol already exists!" });
    }

    // Firestore uniqueness checks (same tenant)
    const [nameSnap, symbolSnap] = await Promise.all([
      houseUnitCollection
        .where("tenantId", "==", tenantId)
        .where("name", "==", cleanName)
        .get(),

      houseUnitCollection
        .where("tenantId", "==", tenantId)
        .where("symbol", "==", symbol.toLowerCase())
        .get(),
    ]);

    if (!nameSnap.empty) {
      return res
        .status(400)
        .json({ message: "House unit name already exists!" });
    }

    if (!symbolSnap.empty) {
      return res
        .status(400)
        .json({ message: "House unit symbol already exists!" });
    }

    // Prepare final payload
    const newDocRef = houseUnitCollection.doc();
    const newHouseUnit = {
      ...validatedData,
      id: newDocRef.id,
      name: cleanName,
      symbol: symbol.toLowerCase(),
      nameNormalized: normalizedName,
    };

    // Save item
    await newDocRef.set(newHouseUnit);

    return res.status(200).json(newHouseUnit);
  } catch (error) {
    console.error("Insert house unit error:", error);
    return res.status(500).json({ message: error.message });
  }
};

exports.getHouseUnits = async (req, res) => {
  try {
    const { tenantId } = req.params;

    if (!tenantId) {
      return res.status(400).json({ message: "Missing tenantId parameter" });
    }

    // Use Firestore query to filter by tenantId
    const snapshot = await houseUnitCollection
      .where("tenantId", "==", tenantId)
      .orderBy("nameNormalized")
      .get();

    const houseUnits = snapshot.docs.map((doc) => doc.data());

    res.status(200).json(houseUnits);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.getHouseUnitById = async (req, res) => {
  try {
    const houseUnit = await houseUnitCollection.doc(req.params.id).get();

    if (!houseUnit.exists)
      return res.status(404).json({ message: "House unit not found" });

    return res.status(200).json(houseUnit.data());
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.updateHouseUnit = async (req, res) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({ message: "Missing house unit ID" });
  }

  try {
    // Validate input body
    const validatedData = handleValidation(req.body, schema);
    if (!validatedData) return;

    const { tenantId, name, symbol } = validatedData;

    // Normalize input early
    const cleanName = trimName(name);

    // Normalized name uniqueness validation
    const { valid, normalizedName, error } = await nameValidation(
      cleanName,
      houseUnitCollection,
      id,
      "tenantId",
      tenantId
    );

    if (!valid) {
      return res.status(400).json({ message: error });
    }

    // Ensure existing record exists
    const unitRef = houseUnitCollection.doc(id);
    const unitDoc = await unitRef.get();

    if (!unitDoc.exists) {
      return res.status(404).json({ message: "House unit not found" });
    }

    // Check against DEFAULT_UNITS
    if (isDuplicateWithDefaultUnits(cleanName, symbol)) {
      return res
        .status(400)
        .json({ message: "House unit name or symbol already exists!" });
    }

    // Check tenant-level uniqueness (excluding this doc)
    const [nameSnap, symbolSnap] = await Promise.all([
      houseUnitCollection
        .where("tenantId", "==", tenantId)
        .where("name", "==", cleanName)
        .get(),

      houseUnitCollection
        .where("tenantId", "==", tenantId)
        .where("symbol", "==", symbol.toLowerCase())
        .get(),
    ]);

    const duplicateName = nameSnap.docs.some((doc) => doc.id !== id);
    const duplicateSymbol = symbolSnap.docs.some((doc) => doc.id !== id);

    if (duplicateName) {
      return res
        .status(400)
        .json({ message: "House unit name already exists!" });
    }

    if (duplicateSymbol) {
      return res
        .status(400)
        .json({ message: "House unit symbol already exists!" });
    }

    // Construct updated payload
    const updatedData = {
      ...validatedData,
      id,
      name: cleanName,
      nameNormalized: normalizedName,
      symbol: symbol.toLowerCase(),
    };

    // Save update
    await unitRef.set(updatedData, { merge: true });

    await propagateHouseUnitName(id, cleanName);

    return res.status(200).json(updatedData);
  } catch (error) {
    console.error("Update house unit error:", error);
    return res.status(500).json({ message: error.message });
  }
};

exports.deleteHouseunit = async (req, res) => {
  try {
    const houseUnit = houseUnitCollection.doc(req.params.id);
    const doc = await houseUnit.get();
    if (!doc.exists) {
      return res.status(404).json({ message: "House unit not found" });
    }
    await houseUnit.delete();
    res.status(200).json("deleted successfully");
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.getAllHouseUnits = async (req, res) => {
  try {
    const { tenantId } = req.params;

    if (!tenantId) {
      return res.status(400).json({ message: "Missing tenantId parameter" });
    }

    // Fetch house units for the given tenantId
    const houseUnitsSnapshot = await houseUnitCollection
      .where("tenantId", "==", tenantId)
      .orderBy("nameNormalized")
      .get();

    const houseUnits = houseUnitsSnapshot.docs.map((doc) => doc.data());

    // Use the DEFAULT_UNITS constant instead of requiring the file
    const allUnits = [...houseUnits, ...DEFAULT_UNITS];

    return res.status(200).json(allUnits);
  } catch (error) {
    console.error("Get house units by tenantId error:", error);
    return res.status(500).json({ message: error.message });
  }
};

// Controller for activating a house unit
exports.activateHouseunit = async (req, res) => {
  try {
    const { tenantId, id } = req.params; // Get house unit ID from URL
    await houseUnitService.activateHouseunit(tenantId, id); // Call service to activate

    // Send success response
    res.status(200).json({
      message: "House Unit activated successfully",
    });
  } catch (err) {
    // Handle errors (e.g., house unit not found)
    res.status(400).json({ message: err.message });
  }
};

// Controller for deactivating a house unit
exports.deactivateHouseunit = async (req, res) => {
  try {
    const { tenantId, id } = req.params;

    const unitSnapshot = await db
      .collection(COLLECTIONS.HOUSE_UNITS)
      .where("tenantId", "==", tenantId)
      .where("id", "==", id)
      .limit(1)
      .get();

    if (unitSnapshot.empty) {
      return res.status(404).json({ message: "House Unit not found" });
    }

    const unitData = unitSnapshot.docs[0].data();
    const symbol = unitData.symbol;

    if (!symbol) {
      return res.status(400).json({ message: "House Unit symbol missing" });
    }
    await houseUnitService.deactivateHouseunit(tenantId, id, symbol);

    res.status(200).json({
      message: `House Unit (${symbol}) deactivated successfully`,
    });
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

exports.getRelatedHouseUnits = async (req, res) => {
  try {
    const tenantId = req.params.tenantId;
    const { unit } = req.body;

    if (!unit || !tenantId) {
      return res.status(400).json({ message: "symbol and tenantId required" });
    }

    const symbols = [unit.symbol, unit.toUnit].filter(Boolean);
    if (symbols.length === 0) {
      return res.status(400).json([]);
    }

    const snap = await db
      .collection("houseUnits")
      .where("tenantId", "==", tenantId)
      .where("activeStatus", "==", true)
      .where("toUnit", "in", symbols)
      .get();

    const units = snap.docs
      .map((d) => ({ id: d.id, ...d.data() }))
      .filter((u) => u.symbol !== unit.symbol);

    res.json(
      units.sort((a, b) => a.nameNormalized.localeCompare(b.nameNormalized))
    );
  } catch (err) {
    console.log(err, "error in getRelatedHouseUnits");
    res.status(400).json({ message: err.message });
  }
};
