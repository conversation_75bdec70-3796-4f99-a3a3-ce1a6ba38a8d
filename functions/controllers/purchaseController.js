// controllers/purchaseController.js

const { receivePO } = require("@/services/purchaseService");

/**
 * Receive items against a PO
 * - Validates items against PO
 * - Updates PO items receivedQty & notes
 * - Persists GRN in Firestore
 * - Calls creditStock for each item to update stock & ledger
 * - Returns the full GRN document
 * @param {Object} body - Request body containing PO details
 * @param {Object} res - Response object
 * @returns {Promise<Object>} - A Promise resolving to a JSON response containing the full GRN document
 */
exports.receivePO = async function (req, res) {
  try {
    const { userName, userId } = req.identity;
    // Call service layer to handle all business logic
    const result = await receivePO({
      ...req.body,
      receivedById: userId,
      receivedByName: userName,
    });

    return res
      .status(200)
      .json({ message: "PO received successfully", result });
  } catch (err) {
    console.error("Error receiving PO:", err);
    return res
      .status(500)
      .json({ message: "Failed to receive PO", error: err.message });
  }
};
