const admin = require("firebase-admin");
const schema = require("@/models/storeSchema");
const { COLLECTIONS } = require("@/defs/collectionDefs");
const db = admin.firestore().collection(COLLECTIONS.LOCATIONS);
const workAreaDb = admin.firestore().collection(COLLECTIONS.WORK_AREAS);

const {
  handleValidation,
  trimName,
  nameValidation,
} = require("@/utils/validation");

const storeServices = require("@/services/storeServices");

const { propagateLocationName } = require("@/utils/updateLinkedData");

exports.insertStoreLogic = async (stores, res) => {
  // Insert stores using batch
  const batch = admin.firestore().batch();

  const validStores = [];
  for (let store of stores) {
    const validatedData = handleValidation(store, schema);
    if (!validatedData) return;

    // Check uniqueness by account.id + store name
    const { valid, normalizedName, error } = await nameValidation(
      validatedData.name,
      db,
      null,
      "tenantId",
      validatedData.tenantId
    );
    if (!valid) {
      console.log("validation failed for store", store);
      return res.status(400).json({ message: error });
    }

    const cleanName = trimName(validatedData.name);

    let storeSnap;

    if (validatedData.posId && validatedData.accountId) {
      storeSnap = await db
        .where("accountId", "==", validatedData.accountId)
        .where("posId", "==", validatedData.posId)
        .limit(1)
        .get();
    } else {
      storeSnap = await db
        .where("tenantId", "==", validatedData.tenantId)
        .where("nameNormalized", "==", normalizedName)
        .limit(1)
        .get();
    }

    const newDocRef = workAreaDb.doc();

    let docRef;
    if (!storeSnap.empty) {
      // Store exists, update it
      const existingDoc = storeSnap.docs[0];
      docRef = db.doc(existingDoc.id);
      batch.update(docRef, {
        ...validatedData,
        id: existingDoc.id,
        name: cleanName,
        nameNormalized: normalizedName,
      });
    } else {
      // Store doesn't exist, insert new one
      docRef = db.doc(); // Auto ID
      batch.set(docRef, {
        ...validatedData,
        id: docRef.id,
        name: cleanName,
        nameNormalized: normalizedName,
        inventoryLocationId: newDocRef.id,
      });
    }
    validStores.push({
      ...validatedData,
      name: cleanName,
      nameNormalized: normalizedName,
    });

    const inventoryLocation = {
      locationId: docRef.id,
      locationName: cleanName,
      id: newDocRef.id,
      name: cleanName,
      nameNormalized: normalizedName,
      tenantId: validatedData.tenantId,
      activeStatus: true,
      isDefault: true,
    };
    await newDocRef.set(inventoryLocation);
  }

  await batch.commit();

  return validStores;
};

exports.insertStores = async (req, res) => {
  try {
    const input = req.body;
    const stores = Array.isArray(input) ? input : [input];

    const accPosId = stores[0].account?.posId;
    let accName;
    let accId;

    if (accPosId) {
      const accountDb = admin.firestore().collection(COLLECTIONS.ACCOUNT);
      const accSnapshot = await accountDb.where("posId", "==", accPosId).get();
      const acc = accSnapshot.docs[0].data();
      accName = acc.name;
      accId = acc.id;
    }

    const modifiedStores = stores.map((store) => {
      return {
        posId: store.posId,
        name: store.name,
        locationType: store.locationType || store.storeType,
        accountId: accId || "",
        accountName: accName || "",
        tenantId: store.tenantId,
        billTo: store?.billTo || "",
        shipTo: store?.shipTo || "",
        panNo: store?.panNo || "",
        gstNo: store?.gstNo || "",
      };
    });

    const validStores = await this.insertStoreLogic(modifiedStores, res);

    res.status(201).json({
      message: "Location Created Successfully",
      count: validStores.length,
    });
  } catch (error) {
    console.error("Error inserting stores:", error);
    res.status(500).json({ message: error.message });
  }
};

exports.getStores = async (req, res) => {
  try {
    const { tenantId } = req.params;
    if (!tenantId)
      return res.status(400).json({ message: "TenantId is Required" });

    const snapshot = await db
      .where("tenantId", "==", tenantId)
      .orderBy("nameNormalized")
      .get();
    const response = snapshot.docs.map((doc) => doc.data());
    res.status(200).json(response);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

exports.getStoreById = async (req, res) => {
  const id = req.params?.id?.trim();
  const tenantId = req.params?.tenantId?.trim();

  if (!id || !tenantId) {
    return res
      .status(400)
      .json({ message: "Both Id and TenantId are Required." });
  }

  try {
    const snapshot = await db
      .where("tenantId", "==", tenantId)
      .where("__name__", "==", id)
      .get();

    if (snapshot.empty) {
      return res.status(404).json({ message: "Location Not Found." });
    }
    const result = [];
    snapshot.forEach((doc) => {
      result.push({ id: doc.id, ...doc.data() });
    });
    return res.status(200).json(result[0]);
  } catch (error) {
    console.error("Error Fetching Location by ID:", error);
    return res.status(500).json({ message: "Internal Server Error." });
  }
};

exports.updateStore = async (req, res) => {
  const id = req.params?.id?.trim();
  const tenantId = req.params?.tenantId?.trim();
  const updatedData = req.body;

  if (!id || !tenantId) {
    return res
      .status(400)
      .json({ message: "Both Id and TenantId are Required." });
  }

  try {
    const validatedData = handleValidation(updatedData, schema);
    if (!validatedData) return;
    const docRef = db.doc(id);
    const docSnap = await docRef.get();

    if (!docSnap.exists) {
      return res.status(404).json({ message: "Location Not Found." });
    }

    const existingStore = docSnap.data();
    if (existingStore?.tenantId !== tenantId) {
      return res
        .status(403)
        .json({ message: "Unauthorized Access to Update This Location." });
    }

    // Name uniqueness check for this account
    const { valid, normalizedName, error } = await nameValidation(
      validatedData.name,
      db,
      id,
      "accountId",
      validatedData.accountId
    );
    if (!valid) return res.status(400).json({ message: error });

    const cleanName = trimName(validatedData.name);

    await docRef.update({
      ...validatedData,
      name: cleanName,
      nameNormalized: normalizedName,
    });

    await propagateLocationName(req.params.id, cleanName);

    return res.status(200).json({ message: "Location Updated Successfully." });
  } catch (error) {
    return res.status(500).json({ message: error.message });
  }
};

// Controller for activating a store
exports.activateStore = async (req, res) => {
  try {
    const { tenantId, id } = req.params; // Get store ID from URL
    const result = await storeServices.activateStore(tenantId, id); // Call service to activate

    // Send success response
    res.status(200).json({
      message: "Location activated successfully",
    });
  } catch (err) {
    // Handle errors (e.g., store not found)
    res.status(400).json({ message: err.message });
  }
};

// Controller for deactivating a store
exports.deactivateStore = async (req, res) => {
  try {
    const { tenantId, id } = req.params; // Get store ID from URL
    const result = await storeServices.deactivateStore(tenantId, id); // Call service to deactivate (with validation)

    // Send success response
    res.status(200).json({
      message: "Location deactivated successfully",
    });
  } catch (err) {
    // Handle first validation error (e.g., linked menu items/orders)
    res.status(400).json({ message: err.message });
  }
};
