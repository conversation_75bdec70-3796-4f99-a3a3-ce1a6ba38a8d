const Joi = require("joi");

const symbolPattern = /^[^\s]{1,10}$/;

const uomSchema = Joi.object({
  id: Joi.string().required(),
  name: Joi.string().max(100).required(),
  symbol: Joi.string()
    .max(10)
    .pattern(symbolPattern)
    .required()
    .messages({ "string.pattern.base": "Invalid symbol" }),
  toUnit: Joi.string().allow(null).optional(),
  quantity: Joi.number()
    .min(0.0001)
    .allow(null)
    .optional()
    .messages({ "number.min": "Quantity must be greater than zero" }),
});

module.exports = uomSchema;
