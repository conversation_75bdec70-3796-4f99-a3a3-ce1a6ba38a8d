const Joi = require("joi");

const userSchema = Joi.object({
  name: Joi.string().required(),
  email: Joi.string().email().required(),
});

const itemSchema = Joi.object({
  itemId: Joi.string().required(),
  itemName: Joi.string().required(),
  itemCode: Joi.string().required(),
  quantity: Joi.number().positive().required(),
  receivedQuantity: Joi.number().positive().required(),
  purchaseUOM: Joi.string().required(),
  unitCost: Joi.number().precision(2).required(),
  taxRate: Joi.number().precision(2).required(),
  totalPrice: Joi.number().precision(2).required(),
});

const statusTimelineEntrySchema = Joi.object({
  name: Joi.string()
    .valid("created", "approved", "rejected", "completed")
    .required(),
  time: Joi.string().isoDate().required(),
  by: userSchema.required(),
});

const vendorSchema = Joi.object({
  id: Joi.string().required(),
  name: Joi.string().required(),
});

const grnSchema = Joi.object({
  location: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().required(),
  }).required(),
  vendor: vendorSchema.required(),
  tenantId: Joi.string().required(),
  items: Joi.array().items(itemSchema).min(1).required(),
  status: Joi.string()
    .valid("created", "approved", "rejected", "completed")
    .optional(),
  prNumber: Joi.string().required(),
  poNumber: Joi.string().required(),
  grnNumber: Joi.string().optional(),
  id: Joi.string().optional(),
  goodsReceivedDate: Joi.string().isoDate().required(),
  vendorInvoiceDate: Joi.string().isoDate().required(),
  vendorInvoiceNumber: Joi.string().required(),
  statusTimeline: Joi.array()
    .items(statusTimelineEntrySchema)
    .min(1)
    .optional(),
  requestedBy: userSchema.required(),
  poOption: Joi.number().valid(1, 2, 3).required(),
  activeStatus: Joi.boolean().default(true),
});

module.exports = grnSchema;
