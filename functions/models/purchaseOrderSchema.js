const Joi = require("joi");
const { purchaseStatus } = require("@/defs/purchaseStatusDefs");
const vendorSchema = require("@/models/vendorSchema");
const { amountSchema } = require("@/models/amountSchema");

const userSchema = Joi.object({
  name: Joi.string().required(),
  id: Joi.string().required(),
});

const timestampSchema = Joi.alternatives().try(
  Joi.date().iso(),
  Joi.number().integer(),
  Joi.object({
    _seconds: Joi.number().integer().required(),
    _nanoseconds: Joi.number().integer().min(0).max(999999999).required(),
  })
);

const itemSchema = Joi.object({
  itemId: Joi.string().required(),
  itemName: Joi.string().required(),
  itemCode: Joi.string().required(),
  quantity: Joi.number().positive().required(),
  receivedQuantity: Joi.number().positive().optional(),
  pkgUOM: Joi.string().allow("", null).optional(),
  purchaseUOM: Joi.string().required(),
  categoryId: Joi.string().required(),
  subcategoryId: Joi.string().required(),
  categoryName: Joi.string().required(),
  subcategoryName: Joi.string().required(),
  pkg: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().required(),
    packageCode: Joi.string().optional(),
    unitCost: Joi.number().optional(),
    quantity: Joi.number().positive().optional(),
  })
    .allow(null)
    .optional(),
  remarks: Joi.string().allow("", null),
  hsnCode: Joi.string().allow("", null),
  contractType: Joi.string().allow("", null),
  contractPrice: Joi.number().precision(2).default(0),
  contractId: Joi.string().allow("", null),
  contractNumber: Joi.string().allow("", null),
  inStock: Joi.number().integer().default(0),
  inclTax: Joi.boolean().default(false),
  unitCost: Joi.number().precision(2).required(),
  ...amountSchema,
});

const statusTimelineEntrySchema = Joi.object({
  name: Joi.string()
    .valid(
      purchaseStatus.DRAFT,
      purchaseStatus.SUBMITTED,
      purchaseStatus.APPROVED,
      purchaseStatus.REJECTED,
      purchaseStatus.COMPLETED,
      purchaseStatus.DELETED,
      purchaseStatus.PARTIAL
    )
    .required(),
  time: timestampSchema.required(),
  by: userSchema.required(),
});

const purchaseOrderSchema = Joi.object({
  location: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().required(),
  }).required(),
  inventoryLocation: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().required(),
  }).optional(),
  tenantId: Joi.string().required(),
  vendor: vendorSchema.required(),
  items: Joi.array().items(itemSchema).min(1).required(),
  requestedBy: userSchema.optional(),
  requestedTime: timestampSchema.optional(),
  updatedBy: userSchema.optional(),
  status: Joi.string()
    .valid(
      purchaseStatus.DRAFT,
      purchaseStatus.SUBMITTED,
      purchaseStatus.APPROVED,
      purchaseStatus.REJECTED,
      purchaseStatus.COMPLETED,
      purchaseStatus.DELETED,
      purchaseStatus.PARTIAL
    )
    .required(),

  statusTimeline: Joi.array()
    .items(statusTimelineEntrySchema)
    .min(1)
    .optional(),
  prNumber: Joi.string().optional(),
  poNumber: Joi.string().optional(),
  id: Joi.string().optional(),
  deliveryDate: timestampSchema.optional(),
  goodsReceivedDate: timestampSchema.optional(),
  vendorInvoiceDate: timestampSchema.optional(),
  vendorInvoiceNumber: Joi.string().optional(),
  rejectedReason: Joi.string().optional(),
  activeStatus: Joi.boolean().default(true),
  lastUpdatedTime: timestampSchema.allow(null).optional(),
  remarks: Joi.string().allow("", null),
  grnDate: Joi.any().optional().allow(null).description("Date of the GRN"),

  // Invoice details
  invoiceDate: Joi.any()
    .optional()
    .allow(null)
    .description("Date of the invoice"),
  invoiceNumber: Joi.string()
    .optional()
    .allow(null, "")
    .description("Invoice number"),
  ...amountSchema,
});

module.exports = purchaseOrderSchema;
