const Joi = require("joi");

const uomSchema = require("@/models/uomSchema");

const ingredientSchema = Joi.object({
  itemId: Joi.string().required(),
  itemName: Joi.string().max(100).required(),
  itemCode: Joi.string().max(50).required(),
  quantity: Joi.number()
    .min(0.0001)
    .required()
    .messages({ "number.min": "Quantity Must be Greater than Zero" }),
  purchaseUnit: uomSchema,
  countingUnit: uomSchema,
  recipeUnit: Joi.alternatives().try(Joi.string(), uomSchema),
  rowNumber: Joi.number().optional(),
  unitCost: Joi.number().min(0).required(),
  isSubRecipe: Joi.boolean().default(false),
});

module.exports = ingredientSchema;
