const Joi = require("joi");

const ingredientSchema = require("@/models/ingredientSchema");
const packageSchema = require("@/models/packageSchema");
const uomSchema = require("@/models/uomSchema");

const inventoryItemSchema = Joi.object({
  id: Joi.string(),
  tenantId: Joi.string().required(),
  itemType: Joi.string().valid("bought", "made").required(),
  itemName: Joi.string().max(100).required(),
  nameNormalized: Joi.string().max(100).optional(),
  itemCode: Joi.string().min(4).max(20).required(),
  category: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().max(100).required(),
  }).required(),
  subCategory: Joi.object({
    id: Joi.string().required(),
    name: Joi.string().max(100).required(),
  }).required(),
  tags: Joi.array()
    .items(
      Joi.object({
        id: Joi.string().optional(),
        name: Joi.string().max(100).optional(),
      })
    )
    .optional(),
  purchaseUnit: uomSchema.required(),
  countingUnit: uomSchema.required(),
  recipeUnit: uomSchema.required(),
  parLevel: Joi.number().min(0).default(0),
  trackExpiry: Joi.boolean().default(false),
  stockable: Joi.boolean().default(true),
  ledger: Joi.string().allow("").optional(),
  defaultPackage: Joi.boolean().default(true),
  showPackage: Joi.boolean().default(false),
  showWeight: Joi.boolean().default(false),
  packages: Joi.alternatives().conditional("showPackage", {
    is: true,
    then: Joi.array().items(packageSchema).min(1).required().messages({
      "array.length": "Minimum one package is required",
    }),

    otherwise: Joi.array().length(0).required().messages({
      "array.length": "Packages must be empty when showPackage is false",
    }),
  }),

  vendors: Joi.array().items(
    Joi.object({
      id: Joi.string().required(),
      name: Joi.string().max(100).required(),
    })
  ),
  taxes: Joi.array()
    .items(
      Joi.object({
        id: Joi.string().required(),
        name: Joi.string().max(100).required(),
      })
    )
    .optional(),
  // only when itemType is made
  ingredients: Joi.alternatives().conditional("itemType", {
    is: "made",
    then: Joi.array()
      .items(ingredientSchema)
      .min(1)
      .required()
      .messages({ "array.min": "At least One Ingredient is Required" }),
    otherwise: Joi.array().items(ingredientSchema).optional(),
  }),
  prepareQuantity: Joi.alternatives().conditional("itemType", {
    is: "made",
    then: Joi.number()
      .min(0.0001)
      .required()
      .messages({ "number.min": "Prepare Quantity Must be Greater than Zero" }),
    otherwise: Joi.number().optional(),
  }),
  activeStatus: Joi.boolean().default(true),
  rowNumber: Joi.number().optional(),
  unitCost: Joi.number().min(0).required(),
  hsnCode: Joi.string().allow("").optional(),
});
module.exports = inventoryItemSchema;
