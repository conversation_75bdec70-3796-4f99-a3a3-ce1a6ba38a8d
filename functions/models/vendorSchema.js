const Joi = require("joi");

const cinPattern = /^[LU][0-9]{5}[A-Z]{2}[0-9]{4}[A-Z]{3}[0-9]{6}$/;
const gstPattern = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
const panPattern = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
const tinPattern = /^(0[1-9]|1\d|2\d|3[0-7])\d{9}$/;

const vendorSchema = Joi.object({
  id: Joi.string(),
  tenantId: Joi.string().required(),
  vendorId: Joi.string().min(4).max(20).required(),
  name: Joi.string().max(100).required(),
  nameNormalized: Joi.string().max(100).optional(),
  contactName: Joi.string().max(100).required(),
  contactNo: Joi.string()
    .allow("", null)
    .pattern(/^\d{10}$/)
    .messages({ "string.pattern.base": "Contact No. is Invalid" }), //10 digit phone number
  contactEmailId: Joi.string().allow("", null).email(),
  cinNo: Joi.string().allow("", null).pattern(cinPattern).messages({
    "string.pattern.base": "CIN Must be a Valid 21-Character",
  }),
  gstNo: Joi.string().allow("", null).pattern(gstPattern).messages({
    "string.pattern.base": "GST Number is Invalid",
  }),
  panNo: Joi.string().allow("", null).pattern(panPattern).messages({
    "string.pattern.base": "PAN Number is Invalid",
  }),
  tinNo: Joi.string().allow("", null).pattern(tinPattern).messages({
    "string.pattern.base": "TIN Number is Invalid",
  }),
  poTerms: Joi.string().allow("", null),
  paymentTerms: Joi.string().allow("", null),
  address: Joi.object({
    address: Joi.string().allow("", null),
    city: Joi.string().allow("", null),
    state: Joi.string().allow("", null),
    pincode: Joi.string()
      .allow("", null)
      .pattern(/^\d{6}$/)
      .messages({ "string.pattern.base": "Pin Code is Invalid" }), // 6 digit PIN
  }),
  activeStatus: Joi.boolean().default(true),
});

module.exports = vendorSchema;
