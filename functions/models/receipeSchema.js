const Joi = require("joi");

const ingredientSchema = require("@/models/ingredientSchema");
const symbolPattern = /^[^\s]{1,10}$/;

const recipeSchema = Joi.object({
  id: Joi.string(),
  tenantId: Joi.string().required(),
  recipeType: Joi.string().default("recipe"),
  name: Joi.string().max(100).required(),
  recipeCode: Joi.string().min(4).max(20).required(),
  nameNormalized: Joi.string().max(100).optional(),
  tags: Joi.array().items(Joi.string().optional()),
  quantity: Joi.number()
    .min(0.0001)
    .required()
    .messages({ "number.min": "Quantity Must be Greater than Zero" }),
  recipeUnit: Joi.string()
    .max(10)
    .pattern(symbolPattern)
    .required()
    .messages({ "string.pattern.base": "Invalid Symbol" }),
  ingredients: Joi.array()
    .items(ingredientSchema)
    .min(1)
    .required()
    .messages({ "array.min": "At Least One Ingredient is Required" }),
  cookingProcedure: Joi.array().items(Joi.string().max(1000)).optional(),
  cost: Joi.number().precision(2).allow(null).optional(),
  activeStatus: Joi.boolean().default(true),
  rowNumber: Joi.number().optional(),
});

module.exports = { recipeSchema, ingredientSchema };
