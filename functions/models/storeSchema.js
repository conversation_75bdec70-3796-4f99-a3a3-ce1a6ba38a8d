const Joi = require("joi");

const gstPattern = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
const panPattern = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;

const storeSchema = Joi.object({
  id: Joi.string().optional(),
  name: Joi.string().required(),
  tenantId: Joi.string().required(),
  inventoryLocationId: Joi.string().optional(),
  locationType: Joi.string().required().uppercase(),
  accountId: Joi.alternatives().conditional("locationType", {
    is: "OUTLET",
    then: Joi.string().required(),
    otherwise: Joi.string().allow("", null).optional(),
  }),
  accountName: Joi.alternatives().conditional("locationType", {
    is: "OUTLET",
    then: Joi.string().required(),
    otherwise: Joi.string().allow("", null).optional(),
  }),
  posId: Joi.string().allow("").optional(), 
  nameNormalized: Joi.string().max(100).optional(),
  activeStatus: Joi.boolean().default(true),
  gstNo: Joi.string().allow("", null).pattern(gstPattern).messages({
    "string.pattern.base": "GST Number is Invalid",
  }),
  panNo: Joi.string().allow("", null).pattern(panPattern).messages({
    "string.pattern.base": "PAN Number is Invalid",
  }),
  billTo: Joi.string().allow("", null),
  shipTo: Joi.string().allow("", null),
});

module.exports = storeSchema;
