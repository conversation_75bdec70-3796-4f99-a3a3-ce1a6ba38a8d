{"indexes": [{"collectionGroup": "stores", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "stores", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "inventoryLocations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "inventoryLocations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "inventoryLocations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "vendors", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "vendors", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "category", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "category", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "houseUnits", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "inventoryItems", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "inventoryItems", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "receipes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "taxes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "nameNormalized", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "tag", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "purchaseRequests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "lastUpdatedTime", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "purchaseOrders", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "lastUpdatedTime", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "contracts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "vendor.id", "order": "ASCENDING"}, {"fieldPath": "endDate", "order": "ASCENDING"}, {"fieldPath": "startDate", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "contracts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "activeStatus", "order": "ASCENDING"}, {"fieldPath": "vendor.id", "order": "ASCENDING"}, {"fieldPath": "endDate", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "stockLedgers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocationId", "order": "ASCENDING"}, {"fieldPath": "itemId", "order": "ASCENDING"}, {"fieldPath": "ledgerType", "order": "ASCENDING"}, {"fieldPath": "pkg.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "stockLedgers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocationId", "order": "ASCENDING"}, {"fieldPath": "itemId", "order": "ASCENDING"}, {"fieldPath": "ledgerType", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocation.id", "order": "ASCENDING"}, {"fieldPath": "location.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "vendorId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocation.id", "order": "ASCENDING"}, {"fieldPath": "location.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "location.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "inventoryLocation.id", "order": "ASCENDING"}, {"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "GRNs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tenantId", "order": "ASCENDING"}, {"fieldPath": "vendorId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}], "fieldOverrides": []}